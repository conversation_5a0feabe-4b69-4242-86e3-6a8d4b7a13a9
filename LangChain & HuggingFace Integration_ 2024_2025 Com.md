<img src="https://r2cdn.perplexity.ai/pplx-full-logo-primary-dark%402x.png" class="logo" width="120"/>

# Lang<PERSON>hain \& HuggingFace Integration: 2024/2025 Compatibility Guide

## 1. Current Stable Version of LangChain

- The **latest stable version** of LangChain as of July 2025 is **v0.3.0**. Users are advised to use `>=0.3,<0.4` for maximum compatibility with ecosystem packages[^1][^2][^3].


## 2. Setting Up HuggingFace Integration with Latest LangChain

### Which Package to Install?

- **Install**: `langchain-huggingface`
    - HuggingFace integration is now moved from `langchain-community` to its own package: `langchain-huggingface`[^1][^4][^5][^6].
- `langchain-community` is deprecated for new integrations.


### Installation Commands

```bash
pip install "langchain>=0.3,<0.4" "langchain-huggingface>=0.1,<0.2" "transformers>=4.40,<5.0" "huggingface-hub>=0.23,<1.0"
```


### Import Statements for Latest Version

```python
from langchain_huggingface import HuggingFacePipeline, ChatHuggingFace
# or for endpoint-based use:
from langchain_huggingface.llms import HuggingFaceEndpoint
```


### Proper Class Names \& Initialization

- **Pipeline-based (recommended for most users):**

```python
llm = HuggingFacePipeline.from_model_id(
    model_id="microsoft/Phi-3-mini-4k-instruct",
    task="text-generation",
    pipeline_kwargs={
        "max_new_tokens": 100,
        "top_k": 50,
        "temperature": 0.1,
    }
)
```

- **Endpoint-based:**

```python
from langchain_huggingface.llms import HuggingFaceEndpoint

llm = HuggingFaceEndpoint(
    repo_id="HuggingFaceH4/zephyr-7b-beta",
    huggingfacehub_api_token="your_hf_token"
)
```


## 3. Changes in HuggingFace Integration (0.2.3 → Latest)

### Breaking Changes

- **Integration packages moved**: Most integrations, including HuggingFace, have migrated from `langchain-community` to their own packages (`langchain-huggingface`). Legacy APIs exist but are deprecated[^1].
- **Deprecation of Pydantic v1**: LangChain now requires Pydantic v2[^1].
- **Changes to initialization**: API tokens must be supplied via the `huggingfacehub_api_token` parameter or a relevant environment variable (`HUGGINGFACEHUB_API_TOKEN`). Omission now triggers an explicit error[^7][^8].
- **Class names**: Use `HuggingFacePipeline`, `ChatHuggingFace`, or `HuggingFaceEndpoint` from `langchain_huggingface`[^4][^6].
- **Some parameters for model initialization have changed**. The specifics depend on the integration method (Pipeline vs endpoint), but `endpoint_url` and `huggingfacehub_api_token` are required for endpoint access, and several former `callback_manager` and `callbacks` parameters are now deprecated or removed[^7][^8].


### HuggingFaceEndpoint Parameter Updates

Key relevant parameters in latest versions:

- `repo_id` (for HuggingFace model repository, required)
- `huggingfacehub_api_token` (required unless environment variable set)
- `endpoint_url`, `do_sample`, `max_new_tokens`, `repetition_penalty`
- No longer supports `callback_manager` (deprecated), use `callbacks`
- Must use environment variables or explicitly pass API token[^7][^8]


## 4. Complete Example (Latest Stable Releases)

### Installation (Python 3.9+ Recommended)

```bash
pip install "langchain>=0.3,<0.4"
pip install "langchain-huggingface>=0.1,<0.2"
pip install "transformers>=4.40,<5.0"
pip install "huggingface-hub>=0.23,<1.0"
```


### Environment Variable for API Token

```python
import os

os.environ["HUGGINGFACEHUB_API_TOKEN"] = "YOUR_HF_TOKEN"
```

Or pass it directly to the model class.

### Import \& Inference Example (Pipeline)

```python
from langchain_huggingface import HuggingFacePipeline

llm = HuggingFacePipeline.from_model_id(
    model_id="microsoft/Phi-3-mini-4k-instruct",
    task="text-generation",
    pipeline_kwargs={
        "max_new_tokens": 128,
        "temperature": 0.1,
    }
)

response = llm.invoke("What is LangChain?")
print(response)
```


### Import \& Inference Example (Endpoint)

```python
from langchain_huggingface.llms import HuggingFaceEndpoint

llm = HuggingFaceEndpoint(
    repo_id="HuggingFaceH4/zephyr-7b-beta",
    huggingfacehub_api_token="YOUR_HF_TOKEN"
)
response = llm.invoke("Explain RAG (Retrieval-Augmented Generation)")
print(response)
```


## 5. Compatible Version Combinations

| Package | Latest Stable | Recommended Version | Notes |
| :-- | :-- | :-- | :-- |
| langchain | 0.3.0 | >=0.3,<0.4 | Required |
| langchain-huggingface | 0.1.0 | >=0.1,<0.2 | Required for HF integration |
| transformers | 4.41.0 | >=4.40,<5.0 | Used for model downloads/running |
| huggingface-hub | 0.23.1 | >=0.23,<1.0 | Used for model repository access |
| langchain-community | 0.3.0 | Legacy only | Deprecated for new integrations |

[^1][^3][^4][^5][^6]

## 6. Known Issues: InferenceClient \& `.post` Method

- The `InferenceClient.post` method in `huggingface-hub` is **deprecated**. Using it triggers warnings[^9].
- Some tasks (like cross-encoding/sentence ranking) are not natively supported by the current friendly task methods, so users have used `.post` as a workaround.
- **If you see `'InferenceClient' object has no attribute 'post'`**, your version might have fully removed support. Upgrade your task to use new task-specific methods (like `.text_classification`, `.sentence_similarity`) or create a custom service. For missing features, consider opening an issue on the Hub repo[^10][^9][^11].


## 7. Migrating Older Tutorials to Modern LangChain

- **Do NOT use old `langchain-community` imports for HuggingFace models.**
- Replace:
    - `from langchain_community.llms import HuggingFaceHub`
    - with
    - `from langchain_huggingface.llms import HuggingFaceEndpoint` *or* `from langchain_huggingface import HuggingFacePipeline, ChatHuggingFace`
- Move any custom initialization code to use API tokens explicitly, not via deprecated environment variables.
- Update your code for Pydantic v2 compatibility if you use custom schema or models.
- Use the `langchain-cli` tool to automatically rewrite imports for compatibility with the latest version[^1].
- If you encounter a breaking change in parameter names or required arguments, refer to the new package’s API reference or documentation.
- For specific migration cases, consult the [LangChain migration guides and changelogs][^1][^8].

**TL;DR:**
> Use `langchain-huggingface` `>=0.1,<0.2` with LangChain `>=0.3,<0.4`.
> Use `HuggingFacePipeline` or `HuggingFaceEndpoint` with explicit API tokens.
> Do not use deprecated `.post` on InferenceClient—use supported high-level methods or raise issues if tasks are not yet covered.
> Update your imports, and let the LangChain CLI handle code migration for most breaking changes.

**References**: [^1][^2][^3][^4][^7][^5][^6][^10][^9][^8]

<div style="text-align: center">⁂</div>

[^1]: https://python.langchain.com/docs/versions/v0_3/

[^2]: https://github.com/langchain-ai/langchain/releases

[^3]: https://pypi.org/project/langchain/

[^4]: https://www.linkedin.com/pulse/huggingface-x-langchain-overview-developers-robyn-le-sueur-kbjyf

[^5]: https://docs.illacloud.com/hugging-face-endpoint/

[^6]: https://python.langchain.com/docs/integrations/chat/huggingface/

[^7]: https://python.langchain.com/api_reference/huggingface/llms/langchain_huggingface.llms.huggingface_endpoint.HuggingFaceEndpoint.html

[^8]: https://github.com/langchain-ai/langchain/issues/26085

[^9]: https://github.com/huggingface/huggingface_hub/issues/3055

[^10]: https://js.langchain.com/docs/integrations/text_embedding/transformers

[^11]: https://discuss.huggingface.co/t/getting-error-attributeerror-inferenceclient-object-has-no-attribute-post/156682

[^12]: https://js.langchain.com/docs/versions/release_policy/

[^13]: https://changelog.langchain.com

[^14]: https://huggingface.co/blog/open-source-llms-as-agents

[^15]: https://python.langchain.com/docs/versions/release_policy/

[^16]: https://www.youtube.com/watch?v=1h6lfzJ0wZw

[^17]: https://adasci.org/how-to-integrate-langchain-and-hugging-face-for-open-source-llm-deployment/

[^18]: https://huggingface.co/docs/huggingface_hub/en/guides/inference_endpoints

[^19]: https://api.python.langchain.com/en/latest/embeddings/langchain_community.embeddings.huggingface.HuggingFaceEmbeddings.html

[^20]: https://huggingface.co/docs/transformers/en/installation

