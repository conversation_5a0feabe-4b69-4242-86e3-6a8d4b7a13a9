
#  Hugging Face models in LangChain!

```
Exercise ID 1656531
```

##  Assignment 

There are thousands of language models freely available to use on [Hugging Face](https://huggingface.co/models). Hugging Face integrates really nicely into <PERSON><PERSON>hai<PERSON>, so in this exercise, you'll use <PERSON><PERSON><PERSON><PERSON> to load and predict using a model from Hugging Face.

To complete this exercise, you'll need first need to create a **free** Hugging Face API token.

1. Sign up for a Hugging Face account at `https://huggingface.co/join`
1. Navigate to `https://huggingface.co/settings/tokens`
1. Select "New token" and copy the key

<img src="https://assets.datacamp.com/production/repositories/6487/datasets/f43d687d38db657069c547855f5a79bb12a28fbf/hf-signup.png" alt="The Hugging Face webpage for creating new tokens.">

##  Instructions 

- Assign your Hugging Face API key to `huggingfacehub_api_token`.
- Define an LLM using the Falcon-7B instruct model from Hugging Face, which has the ID: `'tiiuae/falcon-7b-instruct'`.
- Use `llm` to predict the next words after the text in `question`.



```
from langchain_huggingface import HuggingFaceEndpoint

# Set your Hugging Face API token 
huggingfacehub_api_token = '____'

# Define the LLM
llm = ____(repo_id='____', huggingfacehub_api_token=huggingfacehub_api_token)

# Predict the words following the text in question
question = 'Whatever you do, take care of your shoes'
output = ____

print(output)
```

##  Hints 

- To predict using an LLM in LangChain, call the `.invoke()` method on the LLM object, passing it the input text.



##  Solution 

```
from langchain_huggingface import HuggingFaceEndpoint

# Set your Hugging Face API token 
huggingfacehub_api_token = '<HUGGING_FACE_TOKEN>'

# Define the LLM
llm = HuggingFaceEndpoint(repo_id='tiiuae/falcon-7b-instruct', huggingfacehub_api_token=huggingfacehub_api_token)

# Predict the words following the text in question
question = 'Whatever you do, take care of your shoes'
output = llm.invoke(question)

print(output)
```


