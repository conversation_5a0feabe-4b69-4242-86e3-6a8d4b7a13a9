
#  OpenAI models in LangChain!

```
Exercise ID 1656532
```

##  Assignment 

OpenAI's models are particularly well-regarded in the AI/LLM community; their high performance is largely part to the use of their proprietary technology and carefully selected training data. In contrast to the open-source models on Hugging Face, OpenAI's models do have costs associated with their use, but for many applications, they are currently the best choice to build on.

Due to LangChain's unified syntax, swapping one model for another only requires changing a small amount of code. In this exercise, you'll do just that!

To use OpenAI's models, you'll need an OpenAI API key. If you haven't created one of these before, first, visit their [signup page](https://platform.openai.com/signup). Next, navigate to the [API keys page](https://platform.openai.com/account/api-keys) to create your secret key. If you've lost your key, you can create a new one here, too.

<img src="https://assets.datacamp.com/production/repositories/6309/datasets/842da12a5b68c9f3240978dcfb08726b57ee2a18/api-key-page.png" alt="The button to create a new secret key." width="100%">

OpenAI sometimes provides free credits to new users of the API, but this can differ depending on geography. You may also need to add debit/credit card details depending on geography and available credit. **You'll need less than $1 credit to complete this course.**

##  Instructions 

- Assign your OpenAI API key to `openai_api_key`.
- Define an LLM using the default OpenAI model available on LangChain.
- Use the OpenAI `llm` to predict the next words after the text in `question`.



```
from langchain_openai import OpenAI

# Set your API Key from OpenAI
openai_api_key = "____" 

# Define the LLM
llm = ____(model_name="gpt-3.5-turbo-instruct", openai_api_key=openai_api_key)

# Predict the words following the text in question
question = 'Whatever you do, take care of your shoes'
output = ____

print(output)
```

##  Hints 

- The `OpenAI` function can be used to access OpenAI models in LangChain.
- Like other LLMs in LangChain, the `.invoke()` method is used for text completion/prediction.



##  Solution 

```
from langchain_openai import OpenAI

# Set your API Key from OpenAI
openai_api_key = "<OPENAI_API_TOKEN>" 

# Define the LLM
llm = OpenAI(model_name="gpt-3.5-turbo-instruct", openai_api_key=openai_api_key)

# Predict the words following the text in question
question = 'Whatever you do, take care of your shoes'
output = llm.invoke(question)

print(output)
```


