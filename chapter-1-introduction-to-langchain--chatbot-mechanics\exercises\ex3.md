
#  Prompt templates and chaining

```
Exercise ID 1656534
```

##  Assignment 

In this exercise, you'll begin using two of the core components in LangChain: prompt templates and chains!

**Prompt templates** are used for creating prompts in a more modular way, so they can be reused and built on. **Chains** act as the glue in LangChain; bringing the other components together into workflows that pass inputs and outputs between the different components.

The classes necessary for completing this exercise, including `HuggingFaceEndpoint`, have been pre-loaded for you.

##  Pre exercise code 

```
from langchain_huggingface import HuggingFaceEndpoint
from langchain.prompts import PromptTemplate
```



##  Instructions 

- Assign your Hugging Face API key to `huggingfacehub_api_token`.
- Convert the `template` text provided into a LangChain prompt template.
- Create a chain to integrate the prompt template and LLM.



```
# Set your Hugging Face API token
huggingfacehub_api_token = '____'

# Create a prompt template from the template string
template = "You are an artificial intelligence assistant, answer the question. {question}"
prompt = ____

# Create a chain to integrate the prompt template and LLM
llm = HuggingFaceEndpoint(repo_id='tiiuae/falcon-7b-instruct', huggingfacehub_api_token=huggingfacehub_api_token)
llm_chain = ____

question = "How does LangChain make LLM application development easier?"
print(llm_chain.invoke({"question": question}))
```

##  Hints 

- Input variables present in the template text must be specified as a list to the `input_variables` argument of `PromptTemplate()`.
- To chain a prompt template and LLM together using LCEL, separate the components with the `|` operator.



##  Solution 

```
# Set your Hugging Face API token
huggingfacehub_api_token = '<HUGGING_FACE_TOKEN>'

# Create a prompt template from the template string
template = "You are an artificial intelligence assistant, answer the question. {question}"
prompt = PromptTemplate(template=template, input_variables=["question"])

# Create a chain to integrate the prompt template and LLM
llm = HuggingFaceEndpoint(repo_id='tiiuae/falcon-7b-instruct', huggingfacehub_api_token=huggingfacehub_api_token)
llm_chain = prompt | llm

question = "How does LangChain make LLM application development easier?"
print(llm_chain.invoke({"question": question}))
```


