
#  Chat prompt templates

```
Exercise ID 1656535
```

##  Assignment 

Given the importance of chat models in many different LLM applications, Lang<PERSON>hain provides functionality for accessing chat-specific models and chat prompt templates.

In this exercise, you'll define a chat model from OpenAI, and create a prompt template for it to begin sending it user input questions.

All of the <PERSON><PERSON><PERSON><PERSON> classes necessary for completing this exercise have been pre-loaded for you.

##  Pre exercise code 

```
from langchain_openai import Chat<PERSON>penA<PERSON>
from langchain_core.prompts import Chat<PERSON>romptTemplate
```



##  Instructions 

- Assign your OpenAI API key to `openai_api_key`.
- Define an LLM using an OpenAI **chat** model.
- Assign appropriate roles to the messages provided and convert them into a **chat prompt template**.
- Create a chain with LCEL and invoke it with the input provided.



```
# Set your API Key from OpenAI
openai_api_key= '____'

# Define an OpenAI chat model
llm = ____(temperature=0, openai_api_key=openai_api_key)		

# Create a chat prompt template
prompt_template = ____(
    [
        ("____", "You are a helpful assistant."),
        ("____", "Respond to question: {question}")
    ]
)

# Chain the prompt template and model, and invoke the chain
llm_chain = ____
response = chain.____({"____": "How can I retain learning?"})
print(response.content)
```

##  Hints 

- `"system"`, `"human"`, and `"ai"` are the most common chat message roles, representing messages to define the model behavior, a user input, and a mode output, respectively.
- To convert the messages stored as a list of tuples into a chat prompt template, call `ChatPromptTemplate.from_messages()`.



##  Solution 

```
# Set your API Key from OpenAI
openai_api_key= '<OPENAI_API_TOKEN>'

# Define an OpenAI chat model
llm = ChatOpenAI(temperature=0, openai_api_key=openai_api_key)		

# Create a chat prompt template
prompt_template = ChatPromptTemplate.from_messages(
    [
        ("system", "You are a helpful assistant."),
        ("human", "Respond to question: {question}")
    ]
)

# Chain the prompt template and model, and invoke the chain
llm_chain = prompt_template | llm
response = llm_chain.invoke({"question": "How can I retain learning?"})
print(response.content)
```


