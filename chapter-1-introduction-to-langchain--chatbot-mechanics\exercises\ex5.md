
#  Integrating a chatbot message history

```
Exercise ID 1656537
```

##  Assignment 

A key feature of chatbot applications is the ability to have a conversation, where context from the conversation history is stored and available for the model to access.

In this exercise, you'll create a conversation history that will be passed to the model. This history will contain every message in the conversation, including the user inputs and model responses.

All of the LangChain classes necessary for completing this exercise have been pre-loaded for you.

##  Pre exercise code 

```
from langchain.memory import ChatMessageHistory
from langchain.chat_models import ChatOpenAI
```



##  Instructions 

- Assign your OpenAI API key to `openai_api_key`.
- Create a conversation history and add the first AI message.
- Add the extra user message to the conversation history.
- Add the final user message and call the model on the updated message history.



```
# Set your API Key from OpenAI
openai_api_key= '____'
llm = ChatOpenAI(temperature=0, openai_api_key=openai_api_key)

# Create the conversation history and add the first AI message
history = ____
history.____("Hello! Ask me anything about Python programming!")

# Add the user message to the history
history.____("What is a list comprehension?")

# Add another user message and call the model
history.____("Describe the same in fewer words")
response = ____
print(response.content)
```

##  Hints 

- Messages for particular roles can be added to the history using methods of the form: `.add_{role}_message()`, where `{role}` is replaced by a valid chat message role.
- The messages can be extracted from the conversation history using the `.messages` attribute.



##  Solution 

```
# Set your API Key from OpenAI
openai_api_key= '<OPENAI_API_TOKEN>'
llm = ChatOpenAI(temperature=0, openai_api_key=openai_api_key)

# Create the conversation history and add the first AI message
history = ChatMessageHistory()
history.add_ai_message("Hello! Ask me anything about Python programming!")

# Add the user message to the history
history.add_user_message("What is a list comprehension?")

# Add another user message and call the model
history.add_user_message("Describe the same in fewer words")
ai_response = llm.invoke(history.messages)
print(ai_response.content)
```


