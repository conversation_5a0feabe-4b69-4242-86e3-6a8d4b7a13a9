
#  Creating a memory buffer

```
Exercise ID 1656538
```

##  Assignment 

For many applications, storing and accessing the entire conversation history isn't technically feasible. In these cases, the messages must be condensed while retaining as much relevant context as possible. One common way of doing this is with a memory buffer, which stores only the most recent messages.

In this exercise, you'll integrate a memory buffer into an OpenAI chat model using an LCEL chain.

All of the LangChain classes necessary for completing this exercise have been pre-loaded for you.

##  Pre exercise code 

```
from langchain.memory import ConversationBufferMemory
from langchain_openai import OpenAI
from langchain.chains import Conversation<PERSON>hain
```



##  Instructions 

- Assign your OpenAI API key to `openai_api_key`.
- Define a buffer memory that stores the **four** most recent messages.
- Define a conversation chain for integrating the model and memory buffer.
- Invoke the chain twice with the inputs provided.



```
# Set your API Key from OpenAI
openai_api_key = '____'
chat = OpenAI(model_name="gpt-3.5-turbo-instruct", temperature=0, openai_api_key=openai_api_key)

# Define a buffer memory
memory = ____

# Define the chain for integrating the memory with the model
buffer_chain = ____

# Invoke the chain with the inputs provided
____("Write Python code to draw a scatter plot.")
____("Use the Seaborn library.")
```

##  Hints 

- The `ConversationBufferMemory` class can be used to define the buffer memory.
- The `size` argument of `ConversationBufferMemory()` is used to set the number of messages to store.



##  Solution 

```
# Set your API Key from OpenAI
openai_api_key = '<OPENAI_API_TOKEN>'
chat = OpenAI(model_name="gpt-3.5-turbo-instruct", temperature=0, openai_api_key=openai_api_key)

# Define a buffer memory
memory = ConversationBufferMemory(size=4)

# Define the chain for integrating the memory with the model
buffer_chain = ConversationChain(llm=chat, memory=memory)

# Invoke the chain with the inputs provided
buffer_chain.invoke("Write Python code to draw a scatter plot.")
buffer_chain.invoke("Use the Seaborn library.")
```


