
#  Implementing a summary memory

```
Exercise ID 1656539
```

##  Assignment 

For longer conversations, storing the entire memory, or even a long buffer memory, may not be technically feasible. In these cases, a summary memory implementation can be a good option. Summary memories summarize the conversation at each step to retain only the key context for the model to use. This works by using another LLM for generating the summaries, alongside the LLM used for generating the responses.

In this exercise, you'll implement a chatbot summary memory, using an OpenAI chat model for generating the summaries.

All of the LangChain classes necessary for completing this exercise have been pre-loaded for you.

##  Pre exercise code 

```
from langchain.memory import ConversationSummaryMemory
from langchain_openai import ChatOpenAI
from langchain.chains import Conversation<PERSON>hain
```



##  Instructions 

- Assign your OpenAI API key to `openai_api_key`.
- Define a summary memory that uses the same OpenAI chat model for generating the summaries.
- Define a conversation chain for integrating the model and summary memory; set `verbose` to `True`.
- Invoke the chain twice with the inputs provided.



```
# Set your API Key from OpenAI
openai_api_key = '____'
chat = ChatOpenAI(model_name="gpt-3.5-turbo", temperature=0, openai_api_key=openai_api_key)

# Define a summary memory that uses an OpenAI chat model
memory = ____(llm=ChatOpenAI(model_name="gpt-3.5-turbo", openai_api_key=openai_api_key))

# Define the chain for integrating the memory with the model
summary_chain = ____

# Invoke the chain with the inputs provided
____("Describe the relationship of the human mind with the keyboard when taking a great online class.")
____("Use an analogy to describe it.")
```

##  Hints 

- The `ConversationSummaryMemory` class is used to define the summary memory.
- `ConverationChain()` can again be used to integrate the memory and the model.



##  Solution 

```
# Set your API Key from OpenAI
openai_api_key = '<OPENAI_API_TOKEN>'
chat = ChatOpenAI(model_name="gpt-3.5-turbo", temperature=0, openai_api_key=openai_api_key)

# Define a summary memory that uses an OpenAI chat model
memory = ConversationSummaryMemory(llm=ChatOpenAI(model_name="gpt-3.5-turbo", openai_api_key=openai_api_key))

# Define the chain for integrating the memory with the model
summary_chain = ConversationChain(llm=chat, memory=memory, verbose=True)

# Invoke the chain with the inputs provided
summary_chain.invoke("Describe the relationship of the human mind with the keyboard when taking a great online class.")
summary_chain.invoke("Use an analogy to describe it.")
```


