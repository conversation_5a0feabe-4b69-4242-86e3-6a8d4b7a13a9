Hello,

I'm <PERSON>, an AI Engineer and active contributor to LangChain. Welcome to the course!

We now live in a world where we're spoiled for choice when it comes to choosing large language models, or LLMs, for developing AI-powered applications. However, these LLMs differ based on their model architecture, training data, intended use cases, and prompting strategies. Additionally, these models often have to interact with other systems to retrieve data or monitor performance, which adds another layer of complexity.

LangChain is an open-source framework that helps developers connect LLMs, data sources, and other functionality under a single, unified syntax.

With LangChain, developers can create scalable, modular LLM applications with greater ease.

This course will cover LangChain in Python, but libraries also exist for JavaScript.

LangChain encompasses an entire ecosystem of tools, but in this course, we'll focus on the core components of the LangChain library: LLMs, including open-source and proprietary models, prompts, chains, agents, and document retrievers. Throughout the course, you'll see how these LangChain components can enable some truly awesome LLM applications!

Let's first discuss LLMs.

Hugging Face is a huge repository of open source datasets, tools, and most importantly for us, models!

Accessing LLMs hosted on Hugging Face is free, but using them in LangChain requires creating a Hugging Face API key.

To create one, log in or create a Hugging Face account, and navigate to the URL shown under settings. Here, click on 'New token' and copy your key. DataCamp won't store the API keys you'll need in this course, so they can be directly copied into the exercises.

Now we have our key, let's use LangChain to use a model from Hugging Face, and compare it to using an OpenAI model.

LangChain has OpenAI and HuggingFace classes for interacting with the respective APIs. 

Once imported, we define the llm with the model name and API key. From Hugging Face, we're using the Falcon 7b parameter instruction-optimized model.

We'll define an unfinished sentence, and use both models to predict the next words. Finally, let's print the result to see the output.

Compare the two different approaches - despite using completely different models from different APIs, LangChain unifies them both into a consistent, modular workflow.

To summarize, LangChain is a fantastic tool for developing and orchestrating natural language systems. In the real world of development for production, it unlocks the ability for intelligent conversations with documents, more opportunities for task automation, and different ways to analyze text data. LangChain makes implementing AI more intuitive and gives us greater control over the entire workflow.

Note that we will use a specific LangChain version for this course. If you wish to use a newer version on your own system, be sure to check the LangChain docs for any changes.

Let's get started!

