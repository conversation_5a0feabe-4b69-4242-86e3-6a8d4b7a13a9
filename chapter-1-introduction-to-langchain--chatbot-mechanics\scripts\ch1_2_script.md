Welcome back! Let's use <PERSON><PERSON><PERSON><PERSON> to start implementing prompting strategies for chatbots.

Thousands of LLMs are available in LangChain via the Hugging Face Hub API.

To find language models specifically optimized for chat, search the models section of Hugging Face and filter on Question Answering. Then, take note of the model name so it can be referenced in the code.

New models are constantly being released on Hugging Face. Many are also fine-tuned on domain-specific datasets, so they are better at capturing the nuance of a particular region, culture, or task, so it's worthwhile to spend time searching Hugging Face for the most appropriate model.

Once we've selected a model, we can begin prompting it by utilizing prompt templates.

Prompt templates act as reusable recipes for generating prompts from user inputs in a flexible and modular way.

Templates can include instructions, examples, or any additional context that might help the model complete the task.

Prompt templates are created using <PERSON><PERSON>hai<PERSON>'s PromptTemplate class.

We start by creating a template string, which is structured to prompt the AI to answer a question. The curly braces indicate that we'll use dynamic insertion to insert a variable into the string later in the code.

To convert this string into a prompt template, we pass it to PromptTemplate, specifying any variables representing inputs using the input_variables argument.

To show how a variable will be inserted, let's call the .invoke() method on the prompt template and pass it a dictionary to map values to input variables. We can see in the output how the question placeholder has been replaced.

Let's integrate this with an LLM.

We start by choosing a question-answering LLM from Hugging Face. 

To integrate the prompt_template and model, we use LangChain Expression Language, or LCEL. Using a pipe creates a chain, which, in LangChain, are used to connect a series of calls to different components into a sequence.

To pass an input to this chain, we call the .invoke() method again with the same dictionary as before. This passes the question into the prompt template, then passes the prompt template into the model for a response.

Chat models have a different prompt template class: ChatPromptTemplate.

This allows us to specify a series of messages to pass to the chat model. This is structured as a list of tuples, where each tuple contains a role and message pair. This list is then passed to the .from_messages() method to create the template.

In this example, we can see three roles being used: system, human, and ai. The system role is used to define the model behavior, the human role is used for providing inputs, and the ai role is used for defining outputs, which is often used to provide additional examples for the model.

The ChatOpenAI class is used to access OpenAI's chat models. When instantiating the model, make sure to provide an OpenAI API key.

We create our chain again using an LCEL pipe, define a user input, and invoke the chain as before.

The output is pretty long, but we could tweak our prompt template to change this.

Now it's your turn!

