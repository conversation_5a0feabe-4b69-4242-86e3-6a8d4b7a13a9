Welcome back!

Managing memory is important for conversations with chat models; it opens up the possibility of providing follow-up questions, of building and iterating on model responses, and for adaptation to the user's preferences and behaviors.

Although LangChain allows us to customize and optimize in-conversation chatbot memory, it is still limited by the model's context window. An LLM's context window is the amount of input text the model can consider at once when generating a response, and the length of this window varies for different models.

We'll cover three LangChain classes for implementing chatbot memory: ChatMessageHistory, ConversationBufferMemory, and ConversationSummaryMemory.

ChatMessageHistory stores the full history of messages between the user and model. By providing this to the model, we can provide follow-up questions and iterate on the response message.

Let's implement this message history into an OpenAI model. We first import the ChatMessageHistory and ChatOpenAI classes and define the LLM.

To begin the conversation history, instantiate ChatMessageHistory and store it as a variable.

We'll start our conversation with an AI message, specified using the .add_ai_message() method, which can help set the tone and direction of the conversation.

We can add user messages to the history with the .add_user_message() method.

To provide these messages to the model, invoke the model on the messages attribute of the history. The text response is stored under the .content attribute.

When additional user messages are provided, the model bases its response on the full context stored in the conversation history.

ConversationBufferMemory gives the application a rolling buffer memory containing the last few messages in the conversation.

Users can specify the number of messages to store with the size argument, and the application will discard older messages as newer ones are added.

To integrate the memory type into a model, we use a special type of chain for conversations: ConversationChain.

Let's pass the chain a series of inputs, with the final input asking the model to recall the first question.

The chain returns a dictionary showing the input for the final call, the history the chain has at this point, and the response to the question.

ConversationSummaryMemory summarizes the conversation over time, condensing the information. This allows the chat model to remember key pieces of context without needing to store and process the entire conversation history.

We'll use the ConversationChain again, so we'll first instantiate the model to use for the conversation.

The ConversationSummaryMemory definition is different from ConversationBufferMemory. It also takes an LLM as argument, which is used to create the summaries. So what's happening is that, with each message, a separate LLM call is made to summarize the conversation history.

Defining the conversation chain is the same as before, highlighting the simplicity and modularity of the LangChain framework. We set verbose equal to true to allow the model to output its decisions along with the results.

Let's ask the model three questions in sequence using ConversationSummaryMemory.

The output shows the memory, or history, that will be used to respond to the final input, which we can see is a summary of the first two inputs and responses.

We hope this video was one to remember! Time to practice!

