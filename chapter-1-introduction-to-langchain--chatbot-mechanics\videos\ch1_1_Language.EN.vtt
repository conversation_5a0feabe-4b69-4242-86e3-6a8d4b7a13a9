WEBVTT

1
00:00:00.000 --> 00:00:01.320
Hello,

2
00:00:01.320 --> 00:00:01.360


3
00:00:01.360 --> 00:00:07.280
I'm <PERSON>, an AI Engineer and active contributor to LangChain.

4
00:00:07.280 --> 00:00:09.800
Welcome to the course!

5
00:00:09.800 --> 00:00:13.560
We now live in a world where we're spoiled for choice when it comes to

6
00:00:13.560 --> 00:00:20.320
choosing large language models, or LLMs, for developing AI-powered applications.

7
00:00:20.320 --> 00:00:23.640
However, these LLMs differ based on their model

8
00:00:23.640 --> 00:00:29.800
architecture, training data, intended use cases, and prompting strategies.

9
00:00:29.800 --> 00:00:33.920
Additionally, these models often have to interact with other systems to

10
00:00:33.920 --> 00:00:39.480
retrieve data or monitor performance, which adds another layer of complexity.

11
00:00:39.480 --> 00:00:43.360
LangChain is an open-source framework that helps developers connect

12
00:00:43.360 --> 00:00:50.240
LLMs, data sources, and other functionality under a single, unified syntax.

13
00:00:50.240 --> 00:00:57.160
With LangChain, developers can create scalable, modular LLM applications with greater ease.

14
00:00:57.160 --> 00:01:03.720
This course will cover LangChain in Python, but libraries also exist for JavaScript.

15
00:01:03.720 --> 00:01:10.360
LangChain encompasses an entire ecosystem of tools, but in this course, we'll focus on the core components of the

16
00:01:10.360 --> 00:01:13.840
LangChain library: LLMs, including

17
00:01:13.840 --> 00:01:21.800
open-source and proprietary models, prompts, chains, agents, and document retrievers.

18
00:01:21.800 --> 00:01:28.320
Throughout the course, you'll see how these LangChain components can enable some truly awesome LLM applications!

19
00:01:28.320 --> 00:01:31.200
Let's first discuss LLMs.

20
00:01:31.200 --> 00:01:38.800
Hugging Face is a huge repository of open source datasets, tools, and most importantly for us, models!

21
00:01:38.800 --> 00:01:46.920
Accessing LLMs hosted on Hugging Face is free, but using them in LangChain requires creating a Hugging Face API key.

22
00:01:46.920 --> 00:01:55.480
To create one, log in or create a Hugging Face account, and navigate to the URL shown under settings.

23
00:01:55.480 --> 00:01:59.840
Here, click on 'New token' and copy your key.

24
00:01:59.840 --> 00:02:08.040
DataCamp won't store the API keys you'll need in this course, so they can be directly copied into the exercises.

25
00:02:08.040 --> 00:02:16.480
Now we have our key, let's use LangChain to use a model from Hugging Face, and compare it to using an OpenAI model.

26
00:02:16.480 --> 00:02:23.160
LangChain has OpenAI and HuggingFace classes for interacting with the respective APIs.

27
00:02:23.160 --> 00:02:28.600
Once imported, we define the llm with the model name and API key.

28
00:02:28.600 --> 00:02:35.000
From Hugging Face, we're using the Falcon 7b parameter instruction-optimized model.

29
00:02:35.000 --> 00:02:40.800
We'll define an unfinished sentence, and use both models to predict the next words.

30
00:02:40.800 --> 00:02:45.880
Finally, let's print the result to see the output.

31
00:02:45.880 --> 00:02:50.520
Compare the two different approaches - despite using completely different models

32
00:02:50.520 --> 00:02:57.680
from different APIs, LangChain unifies them both into a consistent, modular workflow.

33
00:02:57.680 --> 00:03:04.000
To summarize, LangChain is a fantastic tool for developing and orchestrating natural language systems.

34
00:03:04.000 --> 00:03:08.720
In the real world of development for production, it unlocks the ability for intelligent

35
00:03:08.720 --> 00:03:16.920
conversations with documents, more opportunities for task automation, and different ways to analyze text data.

36
00:03:16.920 --> 00:03:23.560
LangChain makes implementing AI more intuitive and gives us greater control over the entire workflow.

37
00:03:23.560 --> 00:03:27.640
Note that we will use a specific LangChain version for this course.

38
00:03:27.640 --> 00:03:34.680
If you wish to use a newer version on your own system, be sure to check the LangChain docs for any changes.

39
00:03:34.680 --> 00:03:37.240
Let's get started!

