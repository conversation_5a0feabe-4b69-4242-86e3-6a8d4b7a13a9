WEBVTT

1
00:00:00.000 --> 00:00:02.600
Welcome back!

2
00:00:02.600 --> 00:00:08.280
Let's use LangChain to start implementing prompting strategies for chatbots.

3
00:00:08.280 --> 00:00:13.480
Thousands of LLMs are available in LangChain via the Hugging Face Hub API.

4
00:00:13.480 --> 00:00:18.160
To find language models specifically optimized for chat, search

5
00:00:18.160 --> 00:00:22.400
the models section of Hugging Face and filter on Question Answering.

6
00:00:22.400 --> 00:00:27.040
Then, take note of the model name so it can be referenced in the code.

7
00:00:27.040 --> 00:00:29.920
New models are constantly being released on Hugging Face.

8
00:00:29.920 --> 00:00:34.520
Many are also fine-tuned on domain-specific datasets, so they are better

9
00:00:34.520 --> 00:00:45.240
at capturing the nuance of a particular region, culture, or task, so it's worthwhile to spend time searching Hugging Face for the most appropriate model.

10
00:00:45.240 --> 00:00:50.360
Once we've selected a model, we can begin prompting it by utilizing prompt templates.

11
00:00:50.360 --> 00:00:58.200
Prompt templates act as reusable recipes for generating prompts from user inputs in a flexible and modular way.

12
00:00:58.200 --> 00:01:07.360
Templates can include instructions, examples, or any additional context that might help the model complete the task.

13
00:01:07.360 --> 00:01:11.600
Prompt templates are created using LangChain's PromptTemplate class.

14
00:01:11.600 --> 00:01:17.560
We start by creating a template string, which is structured to prompt the AI to answer a question.

15
00:01:17.560 --> 00:01:24.440
The curly braces indicate that we'll use dynamic insertion to insert a variable into the string later in the code.

16
00:01:24.440 --> 00:01:30.120
To convert this string into a prompt template, we pass it to PromptTemplate,

17
00:01:30.120 --> 00:01:35.680
specifying any variables representing inputs using the input_variables argument.

18
00:01:35.680 --> 00:01:40.720
To show how a variable will be inserted, let's call the .invoke() method on

19
00:01:40.720 --> 00:01:46.680
the prompt template and pass it a dictionary to map values to input variables .

20
00:01:46.680 --> 00:01:51.400
We can see in the output how the question placeholder has been replaced.

21
00:01:51.400 --> 00:01:54.160
Let's integrate this with an LLM.

22
00:01:54.160 --> 00:01:59.560
We start by choosing a question-answering LLM from Hugging Face.

23
00:01:59.560 --> 00:02:06.600
To integrate the prompt_template and model, we use LangChain Expression Language, or LCEL.

24
00:02:06.600 --> 00:02:11.560
Using a pipe creates a chain, which, in LangChain, are used to

25
00:02:11.560 --> 00:02:15.800
connect a series of calls to different components into a sequence.

26
00:02:15.800 --> 00:02:24.160
To pass an input to this chain, we call the .invoke() method again with the same dictionary as before.

27
00:02:24.160 --> 00:02:30.920
This passes the question into the prompt template, then passes the prompt template into the model for a response .

28
00:02:30.920 --> 00:02:37.800
Chat models have a different prompt template class: ChatPromptTemplate.

29
00:02:37.800 --> 00:02:42.440
This allows us to specify a series of messages to pass to the chat model.

30
00:02:42.440 --> 00:02:48.800
This is structured as a list of tuples, where each tuple contains a role and message pair.

31
00:02:48.800 --> 00:02:54.600
This list is then passed to the .from_messages() method to create the template.

32
00:02:54.600 --> 00:03:01.240
In this example, we can see three roles being used: system, human, and ai.

33
00:03:01.240 --> 00:03:08.080
The system role is used to define the model behavior, the human role is used for providing inputs, and

34
00:03:08.080 --> 00:03:15.360
the ai role is used for defining outputs, which is often used to provide additional examples for the model .

35
00:03:15.360 --> 00:03:20.520
The ChatOpenAI class is used to access OpenAI's chat models.

36
00:03:20.520 --> 00:03:26.240
When instantiating the model, make sure to provide an OpenAI API key.

37
00:03:26.240 --> 00:03:33.800
We create our chain again using an LCEL pipe, define a user input, and invoke the chain as before.

38
00:03:33.800 --> 00:03:39.520
The output is pretty long, but we could tweak our prompt template to change this.

39
00:03:39.520 --> 00:03:42.080
Now it's your turn.

40
00:03:42.080 --> 00:03:42.120
.

