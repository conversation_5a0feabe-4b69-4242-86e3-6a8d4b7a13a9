WEBVTT

1
00:00:00.000 --> 00:00:03.240
Welcome back!

2
00:00:03.240 --> 00:00:07.680
Managing memory is important for conversations with chat models; it opens up

3
00:00:07.680 --> 00:00:18.560
the possibility of providing follow-up questions, of building and iterating on model responses, and for adaptation to the user's preferences and behaviors.

4
00:00:18.560 --> 00:00:21.520
Although LangChain allows us to customize and optimize

5
00:00:21.520 --> 00:00:27.640
in-conversation chatbot memory, it is still limited by the model's context window.

6
00:00:27.640 --> 00:00:33.680
An LLM's context window is the amount of input text the model can consider at once

7
00:00:33.680 --> 00:00:39.320
when generating a response, and the length of this window varies for different models.

8
00:00:39.320 --> 00:00:43.680
We'll cover three LangChain classes for implementing chatbot memory:

9
00:00:43.680 --> 00:00:51.120
ChatMessageHistory, ConversationBufferMemory, and ConversationSummaryMemory.

10
00:00:51.120 --> 00:00:56.880
ChatMessageHistory stores the full history of messages between the user and model.

11
00:00:56.880 --> 00:01:03.320
By providing this to the model, we can provide follow-up questions and iterate on the response message.

12
00:01:03.320 --> 00:01:07.040
Let's implement this message history into an OpenAI model.

13
00:01:07.040 --> 00:01:14.520
We first import the ChatMessageHistory and ChatOpenAI classes and define the LLM.

14
00:01:14.520 --> 00:01:21.400
To begin the conversation history, instantiate ChatMessageHistory and store it as a variable.

15
00:01:21.400 --> 00:01:25.440
We'll start our conversation with an AI message, specified using the

16
00:01:25.440 --> 00:01:31.600
.add_ai_message() method, which can help set the tone and direction of the conversation .

17
00:01:31.600 --> 00:01:35.600
We can add user messages to the history with the .add_user_message() method.

18
00:01:35.600 --> 00:01:42.880
To provide these messages to the model, invoke the model on the messages attribute of the history .

19
00:01:42.880 --> 00:01:46.920
The text response is stored under the .content attribute.

20
00:01:46.920 --> 00:01:50.960
When additional user messages are provided, the model bases its

21
00:01:50.960 --> 00:01:56.880
response on the full context stored in the conversation history.

22
00:01:56.880 --> 00:02:00.560
ConversationBufferMemory gives the application a rolling

23
00:02:00.560 --> 00:02:05.800
buffer memory containing the last few messages in the conversation .

24
00:02:05.800 --> 00:02:09.240
Users can specify the number of messages to store with the size

25
00:02:09.240 --> 00:02:16.200
argument, and the application will discard older messages as newer ones are added.

26
00:02:16.200 --> 00:02:25.120
To integrate the memory type into a model, we use a special type of chain for conversations: ConversationChain.

27
00:02:25.120 --> 00:02:32.360
Let's pass the chain a series of inputs, with the final input asking the model to recall the first question.

28
00:02:32.360 --> 00:02:36.560
The chain returns a dictionary showing the input for the final call,

29
00:02:36.560 --> 00:02:40.040
the history the chain has at this point, and the response to the question.

30
00:02:40.040 --> 00:02:48.280
ConversationSummaryMemory summarizes the conversation over time, condensing the information.

31
00:02:48.280 --> 00:02:52.000
This allows the chat model to remember key pieces of context

32
00:02:52.000 --> 00:02:56.880
without needing to store and process the entire conversation history.

33
00:02:56.880 --> 00:03:03.840
We'll use the ConversationChain again, so we'll first instantiate the model to use for the conversation.

34
00:03:03.840 --> 00:03:09.440
The ConversationSummaryMemory definition is different from ConversationBufferMemory.

35
00:03:09.440 --> 00:03:14.560
It also takes an LLM as argument, which is used to create the summaries.

36
00:03:14.560 --> 00:03:22.320
So what's happening is that, with each message, a separate LLM call is made to summarize the conversation history.

37
00:03:22.320 --> 00:03:25.520
Defining the conversation chain is the same as before,

38
00:03:25.520 --> 00:03:29.960
highlighting the simplicity and modularity of the LangChain framework.

39
00:03:29.960 --> 00:03:36.880
We set verbose equal to true to allow the model to output its decisions along with the results.

40
00:03:36.880 --> 00:03:42.440
Let's ask the model three questions in sequence using ConversationSummaryMemory.

41
00:03:42.440 --> 00:03:46.840
The output shows the memory, or history, that will be used to respond to the

42
00:03:46.840 --> 00:03:53.280
final input, which we can see is a summary of the first two inputs and responses.

43
00:03:53.280 --> 00:03:55.840
We hope this video was one to remember.

44
00:03:55.840 --> 00:03:57.400
Time to practice.

45
00:03:57.400 --> 00:03:59.200
.

