
#  Building prompts for sequential chains

```
Exercise ID 1760687
```

##  Assignment 

Over the next couple of exercises, you'll work to create a system for helping people learn new skills. This system needs to be built sequentially, so learners can modify plans based on their preferences and constraints. You'll utilize your LangChain LCEL skills to build a sequential chain to build this system, and the first step is to design the **prompt templates** that will be used by this system.

##  Pre exercise code 

```
from langchain_core.prompts import PromptTemplate
```



##  Instructions 

- Create a prompt template called `learning_prompt` that takes an input `"activity"` and creates a learning plan.
- Create a prompt template called `learning_prompt` that takes an input `"learning_plan"` and modifies it to fit within **one week**.
- Invoke the `learning_prompt` with an activity of your choice (try `"play golf"` if you're struggling for ideas).



```
# Create a prompt template that takes an input activity
learning_prompt = ____(
    input_variables=["____"],
    template="I want to learn how to {activity}. Can you suggest how I can learn this step-by-step?"
)

# Create a prompt template that places a time constraint on the output
time_prompt = ____(
    ____,
    template="I only have one week. Can you create a plan to help me hit this goal: {learning_plan}."
)

# Invoke the learning_prompt with an activity
print(learning_prompt.____({"activity": "____"}))
```

##  Hints 

- `PromptTemplate` takes an argument, `input_variables`, for listing the input variables in the template string.



##  Solution 

```
# Create a prompt template that takes an input activity
learning_prompt = PromptTemplate(
    input_variables=["activity"],
    template="I want to learn how to {activity}. Can you suggest how I can learn this step-by-step?"
)

# Create a prompt template that places a time constraint on the output
time_prompt = PromptTemplate(
    input_variables=["learning_plan"],
    template="I only have one week. Can you create a plan to help me hit this goal: {learning_plan}."
)

# Invoke the learning_prompt with an activity
print(learning_prompt.invoke({"activity": "play golf"}))
```


