
#  Sequential chains with LCEL

```
Exercise ID 1760688
```

##  Assignment 

With your prompt templates created, it's time to tie everything together, including the LLM, using chains and LCEL.

For the final step of calling the chain, feel free to insert any activity you wish! If you're struggling for ideas, try inputting `"play the harmonica"`.

##  Pre exercise code 

```
from langchain.chat_models import ChatOpenAI
from langchain.prompts import PromptTemplate
from langchain.schema.output_parser import StrOutputParser
```



##  Instructions 

- Assign your OpenAI API key to `openai_api_key`.
- Create a sequential chain using LCEL that passes `learning_prompt` into the LLM, and feeds the output into `time_prompt` for resending to the LLM.
- Call the chain with the activity of your choice!



```
# Set your API Key from OpenAI
openai_api_key = '____'

learning_prompt = PromptTemplate(
    input_variables=["activity"],
    template="I want to learn how to {activity}. Can you suggest how I can learn this step-by-step?"
)

time_prompt = PromptTemplate(
    input_variables=["learning_plan"],
    template="I only have one week. Can you create a plan to help me hit this goal: {learning_plan}."
)

llm = ChatOpenAI(openai_api_key=openai_api_key)

# Complete the sequential chain with LCEL
seq_chain = ({"____": ____ | ____ | StrOutputParser()}
    | ____
    | ____
    | StrOutputParser())

# Call the chain
print(seq_chain.____({"____": "____"}))
```

##  Hints 

- In LCEL, prompts are piped into LLMs.
- Because `time_prompt` has `"learning_plan"` as an input variable, make sure to assign the first chain to this variable in the dictionary.



##  Solution 

```
# Set your API Key from OpenAI
openai_api_key = '<OPENAI_API_TOKEN>'

learning_prompt = PromptTemplate(
    input_variables=["activity"],
    template="I want to learn how to {activity}. Can you suggest how I can learn this step-by-step?"
)

time_prompt = PromptTemplate(
    input_variables=["learning_plan"],
    template="I only have one week. Can you create a plan to help me hit this goal: {learning_plan}."
)

llm = ChatOpenAI(openai_api_key=openai_api_key)

# Complete the sequential chain with LCEL
seq_chain = ({"learning_plan": learning_prompt | llm | StrOutputParser()}
    | time_prompt
    | llm
    | StrOutputParser())

# Call the chain
print(seq_chain.invoke({"activity": "play the harmonica"}))
```


