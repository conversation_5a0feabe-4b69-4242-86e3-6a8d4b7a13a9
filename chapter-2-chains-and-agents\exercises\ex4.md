
#  ReAct agents

```
Exercise ID 1760691
```

##  Assignment 

Time to have a go at creating your own ReAct agent! Recall that ReAct stands for Reason and Act, which describes how they make decisions. In this exercise, you'll load the built-in `wikipedia` tool to integrate external data from Wikipedia with your LLM.

**Note**: The `wikipedia` tool requires the `wikipedia` Python library to be installed as a dependency, which has been done for you in this case.

##  Pre exercise code 

```
from langchain.agents import load_tools
from langchain_openai import ChatOpenAI
from langgraph.prebuilt import create_react_agent
from langchain.agents import tool
from langchain_community.tools import WikipediaQueryRun
from langchain_community.utilities import WikipediaAPIWrapper

@tool
def wikipedia():
    """A wrapper around Wikipedia. Useful for when you need to answer general questions about people, places, companies, facts, historical events, or other subjects. Input should be a search query."""
    return WikipediaQueryRun(api_wrapper=WikipediaAPIWrapper())
  
old_load_tools = load_tools

def load_tools(tool_names, llm=None):
  if 'wikipedia' in tool_names:
    return [wikipedia]
  else:
    return old_load_tools(tool_names, llm)
```



##  Instructions 

- Assign your OpenAI API key to `openai_api_key`.
- Load the `"wikipedia"` tool.
- Define a ReAct agent, passing it the model and tools to use.
- Run the agent on the input provided and print the content from the final message in `response`.



```
# Set your API Key from OpenAI
openai_api_key = '____'

llm = ChatOpenAI(model_name="gpt-3.5-turbo", temperature=0, openai_api_key=openai_api_key)

# Define the tools
tools = ____(["wikipedia"])

# Define the agent
agent = ____(____, ____)

# Invoke the agent
response = ____({"messages": [("human", "Summarize key facts about London, England.")]})
print(____)
```

##  Hints 

- The `create_react_agent()` function takes an LLM to use and a list of tools to pass to it.
- `response` is a dictionary with the messages stored under the `'messages'` key.
- Each message has a `.content` attribute for extracting the model's output.



##  Solution 

```
# Set your API Key from OpenAI
openai_api_key = '<OPENAI_API_TOKEN>'

llm = ChatOpenAI(model_name="gpt-3.5-turbo", temperature=0, openai_api_key=openai_api_key)

# Define the tools
tools = load_tools(["wikipedia"])

# Define the agent
agent = create_react_agent(llm, tools)

# Invoke the agent
response = agent.invoke({"messages": [("human", "Summarize key facts about London, England.")]})
print(response['messages'][-1].content)
```


