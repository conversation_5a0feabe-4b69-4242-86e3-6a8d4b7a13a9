
#  Defining a function for tool use

```
Exercise ID 1760693
```

##  Assignment 

You're working for a SaaS (software as a service) company with big goals for rolling out tools to help employees at all levels of the organization to make data-informed decisions. You're creating a PoC for an application that allows customer success managers to interface with company data using natural language to retrieve important customer data.

You've been provided with a pandas DataFrame called `customers` that contains a small sample of customer data. Your first step in this project is to define a Python function to extract information from this table given a customer's name. `pandas` has already been imported as `pd`.

##  Pre exercise code 

```
import pandas as pd

customer_dict = {
    "id": [101, 102, 103, 104, 105, 106, 107, 108, 109, 110],
    "name": [
        "Tech Innovators Inc.", 
        "Green Solutions Ltd.", 
        "Global Enterprises", 
        "Peak Performance Co.", 
        "Visionary Ventures", 
        "NextGen Technologies", 
        "Dynamic Dynamics LLC", 
        "Infinity Services", 
        "Eco-Friendly Products", 
        "Future Insights"
    ],
    "subscription_type": [
        "Premium", 
        "Standard", 
        "Basic", 
        "Premium", 
        "Standard", 
        "Basic", 
        "Premium", 
        "Standard", 
        "Basic", 
        "Premium"
    ],
    "active_users": [450, 300, 150, 800, 600, 200, 700, 500, 100, 900],
    "auto_renewal": [True, False, True, True, False, True, True, False, True, True]
}

customers = pd.DataFrame(customer_dict)

del customer_dict
```



##  Instructions 

- Define a `retrieve_customer_info()` function that takes a string argument, `name`. 
- Filter the `customers` DataFrame to return rows with `"name"` equal to the input argument, `name`.
- Call the function on the customer name, `"Peak Performance Co."`.



```
# Define a function to retrieve customer info by-name
def retrieve_customer_info(____: ____) -> str:
    """Retrieve customer information based on their name."""
    # Filter customers for the customer's name
    customer_info = customers[customers['name'] == ____]
    return customer_info.to_string()
  
# Call the function on Peak Performance Co.
print(retrieve_customer_info("____"))
```

##  Hints 

- When defining a Python function, you can restrict input variables to particular data types using the syntax: `function_name(arg: data_type)`.



##  Solution 

```
# Define a function to retrieve customer info by-name
def retrieve_customer_info(name: str) -> str:
    """Retrieve customer information based on their name."""
    # Filter customers for the customer's name
    customer_info = customers[customers['name'] == name]
    return customer_info.to_string()
  
# Call the function on Peak Performance Co.
print(retrieve_customer_info("Peak Performance Co."))
```


