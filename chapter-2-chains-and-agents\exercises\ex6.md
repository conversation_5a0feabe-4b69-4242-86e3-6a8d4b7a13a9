
#  Creating custom tools

```
Exercise ID 1760694
```

##  Assignment 

Now that you have a function for extracting customer data from the `customers` DataFrame, it's time to convert this function into a tool that's compatible with LangChain agents.

##  Pre exercise code 

```
from langchain.agents import tool
import pandas as pd

customer_dict = {
    "id": [101, 102, 103, 104, 105, 106, 107, 108, 109, 110],
    "name": [
        "Tech Innovators Inc.", 
        "Green Solutions Ltd.", 
        "Global Enterprises", 
        "Peak Performance Co.", 
        "Visionary Ventures", 
        "NextGen Technologies", 
        "Dynamic Dynamics LLC", 
        "Infinity Services", 
        "Eco-Friendly Products", 
        "Future Insights"
    ],
    "subscription_type": [
        "Premium", 
        "Standard", 
        "Basic", 
        "Premium", 
        "Standard", 
        "Basic", 
        "Premium", 
        "Standard", 
        "Basic", 
        "Premium"
    ],
    "active_users": [450, 300, 150, 800, 600, 200, 700, 500, 100, 900],
    "auto_renewal": [True, False, True, True, False, True, True, False, True, True]
}

customers = pd.DataFrame(customer_dict)

del customer_dict
```



##  Instructions 

- Modify the function provided so it can be used as a tool.
- Print the tool's arguments using a tool attribute.



```
# Convert the retrieve_customer_info function into a tool
____
def retrieve_customer_info(name: str) -> str:
    """Retrieve customer information based on their name."""
    customer_info = customers[customers['name'] == name]
    return customer_info.to_string()
  
# Print the tool's arguments
print(____)
```

##  Hints 

- The `@tool` decorator, placed before function definitions, modifies Python functions so they can be used as tools.
- The `.args` tool attribute can be use to examine a tool's arguments and data types.



##  Solution 

```
# Convert the retrieve_customer_info function into a tool
@tool
def retrieve_customer_info(name: str) -> str:
    """Retrieve customer information based on their name."""
    customer_info = customers[customers['name'] == name]
    return customer_info.to_string()
  
# Print the tool's arguments
print(retrieve_customer_info.args)
```


