
#  Integrating custom tools with agents

```
Exercise ID 1760695
```

##  Assignment 

Now that you have your tools at-hand, it's time to set up your agentic workflow! You'll again be using a ReAct agent, which, recall, reasons on the steps it should take, and selects tools using this context and the tool descriptions.

##  Pre exercise code 

```
from langchain.agents import tool
from langchain_openai import ChatOpenAI
from langgraph.prebuilt import create_react_agent
import pandas as pd

customer_dict = {
    "id": [101, 102, 103, 104, 105, 106, 107, 108, 109, 110],
    "name": [
        "Tech Innovators Inc.", 
        "Green Solutions Ltd.", 
        "Global Enterprises", 
        "Peak Performance Co.", 
        "Visionary Ventures", 
        "NextGen Technologies", 
        "Dynamic Dynamics LLC", 
        "Infinity Services", 
        "Eco-Friendly Products", 
        "Future Insights"
    ],
    "subscription_type": [
        "Premium", 
        "Standard", 
        "Basic", 
        "Premium", 
        "Standard", 
        "Basic", 
        "Premium", 
        "Standard", 
        "Basic", 
        "Premium"
    ],
    "active_users": [450, 300, 150, 800, 600, 200, 700, 500, 100, 900],
    "auto_renewal": [True, False, True, True, <PERSON>als<PERSON>, <PERSON>, <PERSON>, <PERSON>als<PERSON>, True, True]
}

customers = pd.DataFrame(customer_dict)

del customer_dict
```



##  Instructions 

- Assign your OpenAI API key to `openai_api_key`.
- Create a ReAct agent using your `retrieve_customer_info` tool and an LLM.
- Invoke the agent on the input provided and print the content from the final message in `response`.



```
# Set your API Key from OpenAI
openai_api_key = '____'

llm = ChatOpenAI(model_name="gpt-3.5-turbo", temperature=0, openai_api_key=openai_api_key)

@tool
def retrieve_customer_info(name: str) -> str:
    """Retrieve customer information based on their name."""
    customer_info = customers[customers['name'] == name]
    return customer_info.to_string()

# Create a ReAct agent
agent = ____

# Invoke the agent on the input
messages = ____({"messages": [("human", "Create a summary of our customer: Peak Performance Co.")]})
print(____)
```

##  Hints 

- The `create_react_agent()` function takes an LLM to use, and a list of tools to make accessible to it.



##  Solution 

```
# Set your API Key from OpenAI
openai_api_key = '<OPENAI_API_TOKEN>'

llm = ChatOpenAI(model_name="gpt-3.5-turbo", temperature=0, openai_api_key=openai_api_key)

@tool
def retrieve_customer_info(name: str) -> str:
    """Retrieve customer information based on their name."""
    customer_info = customers[customers['name'] == name]
    return customer_info.to_string()

# Create a ReAct agent
agent = create_react_agent(llm, [retrieve_customer_info])

# Invoke the agent on the input
messages = agent.invoke({"messages": [("human", "Create a summary of our customer: Peak Performance Co.")]})
print(messages['messages'][-1].content)
```


