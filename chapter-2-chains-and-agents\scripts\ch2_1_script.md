Let's take our LCEL chains to the next level with sequential chains!

Some problems can only be solved sequentially.

Consider a chatbot used to create a travel itinerary.

We need to tell the chatbot our destination,

receive suggestions on what to see on our trip,

and tell the model which activities to select

to compile the itinerary.

This is a sequential problem, as it requires more than one user input: one to specify the destination, and another to select the activities. Let's code this out!

In sequential chains, the output from one chain becomes the input to another.

We'll create two prompt templates: one to generate suggestions for activities from the input destination, and another to create an itinerary for one day of activities from the model's top three suggestions.

We define our model, and begin our sequential chain. We start by defining a dictionary that passes our destination prompt template to the LLM and parses the output to a string, all using LCEL's pipe. This gets assigned to the "activities" key, which is important, as this is the input variable to the second prompt template. We pipe the first chain into the second prompt template, then into the LLM, and again, parse to a string. We also wrap the sequential chain in parentheses so we can split this code across multiple lines.

To summarize: the destination_prompt is passed to the LLM to generate the activity suggestions, and the output is parsed to a string and assigned to "activities". This is passed to the second activities_prompt, which is passed to the LLM to generate the itinerary, which is parsed as a string.

Let's invoke the chain, passing Rome as our input destination.

The model considered that we only had one day to explore, and wove in it's top suggestions of the Colosseum and Vatican City.

Time to give this a go!

