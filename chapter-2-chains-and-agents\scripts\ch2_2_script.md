Now that we're confident with LLMs, it's time to move onto a hot area of AI application development: agents.

In LangChain, agents use language models to determine actions.

Agents often use tools, which are functions called by the agent to interact with the system. These tools can be high-level utilities to transform inputs, or they can be task-specific. Agents can even use chains and other agents as tools!

In this video, we'll discuss a type of agent called ReAct agents.

ReAct stands for reasoning and acting, and this is exactly how the agent operates.

It prompts the model using a repeated loop of thinking, acting, and observing.

If we were to ask a ReAct agent that had access to a weather tool, "What is the weather like in Kingston, Jamaica?", it would start by thinking about the task and which tool to call, call that tool using the information, and observe the results from the tool call.

To implement agents, we'll be using LangGraph, which is branch of the LangChain ecosystem specifically for designing agentic systems, or systems including agents.

Like LangChain's core library, it's is built to provide a unified, tool-agnostic syntax. We'll be using the following version in this course.

We'll create a ReAct agent that can solve math problems - something most LLMs struggle with.

We import create_react_agent from langgraph and the load_tools() function.

We initialize our LLM, and load the llm-math tool using the load_tools() function.

To create the agent, we pass the LLM and tools to create_react_agent(),

Just like chains, agents can be executed with the .invoke() method. Here, we pass the chat model a message to find the square root of 101, which isn't a whole number. Let's see how the agent approaches the problem!

There's a lot of metadata in the output, so we've trimmed it for brevity. We can see that executing the agent resulted in a series of messages. The first is our prompt defining the problem; the second is created by the model to identify the tool to use and to convert our query into mathematical format; the third is the result of the tool call, and the final message is the model's response after observing the tool's answer, which it decided to round to two decimal places.

If we just want the final response, we can subset the final message and extract it's content with the .content attribute.

Now let's begin implementing agents!

