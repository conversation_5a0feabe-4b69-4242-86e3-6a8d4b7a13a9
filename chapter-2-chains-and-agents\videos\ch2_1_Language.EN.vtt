WEBVTT

1
00:00:00.000 --> 00:00:06.280
Let's take our LCEL chains to the next level with sequential chains!

2
00:00:06.280 --> 00:00:09.760
Some problems can only be solved sequentially.

3
00:00:09.760 --> 00:00:13.560
Consider a chatbot used to create a travel itinerary.

4
00:00:13.560 --> 00:00:15.760
We need to tell the chatbot our destination,

5
00:00:15.760 --> 00:00:15.840


6
00:00:15.840 --> 00:00:17.600
receive suggestions on what to see

7
00:00:17.600 --> 00:00:18.640
on our trip,

8
00:00:18.640 --> 00:00:18.640


9
00:00:18.640 --> 00:00:21.000
and tell the model which activities to select

10
00:00:21.000 --> 00:00:21.000


11
00:00:21.000 --> 00:00:23.440
to compile the itinerary.

12
00:00:23.440 --> 00:00:27.200
This is a sequential problem, as it requires more than one user

13
00:00:27.200 --> 00:00:33.200
input: one to specify the destination, and another to select the activities.

14
00:00:33.200 --> 00:00:35.400
Let's code this out!

15
00:00:35.400 --> 00:00:40.280
In sequential chains, the output from one chain becomes the input to another.

16
00:00:40.280 --> 00:00:45.840
We'll create two prompt templates: one to generate suggestions for activities from the input

17
00:00:45.840 --> 00:00:54.600
destination, and another to create an itinerary for one day of activities from the model's top three suggestions.

18
00:00:54.600 --> 00:00:58.600
We define our model, and begin our sequential chain.

19
00:00:58.600 --> 00:01:02.680
We start by defining a dictionary that passes our destination prompt

20
00:01:02.680 --> 00:01:10.400
template to the LLM and parses the output to a string, all using LCEL's pipe.

21
00:01:10.400 --> 00:01:13.640
This gets assigned to the "activities" key, which is

22
00:01:13.640 --> 00:01:18.800
important, as this is the input variable to the second prompt template.

23
00:01:18.800 --> 00:01:27.400
We pipe the first chain into the second prompt template, then into the LLM, and again, parse to a string.

24
00:01:27.400 --> 00:01:32.960
We also wrap the sequential chain in parentheses so we can split this code across multiple lines.

25
00:01:32.960 --> 00:01:39.000
To summarize: the destination_prompt is passed to the LLM to generate the

26
00:01:39.000 --> 00:01:46.160
activity suggestions, and the output is parsed to a string and assigned to "activities".

27
00:01:46.160 --> 00:01:49.720
This is passed to the second activities_prompt, which is passed

28
00:01:49.720 --> 00:01:54.640
to the LLM to generate the itinerary, which is parsed as a string.

29
00:01:54.640 --> 00:01:59.640
Let's invoke the chain, passing Rome as our input destination.

30
00:01:59.640 --> 00:02:03.200
The model considered that we only had one day to explore, and

31
00:02:03.200 --> 00:02:07.520
wove in it's top suggestions of the Colosseum and Vatican City.

32
00:02:07.520 --> 00:02:11.400
Time to give this a go!

