WEBVTT

1
00:00:00.000 --> 00:00:10.080
Now that we're confident with LLMs, it's time to move onto a hot area of AI application development: agents.

2
00:00:10.080 --> 00:00:15.200
In LangChain, agents use language models to determine actions.

3
00:00:15.200 --> 00:00:20.600
Agents often use tools, which are functions called by the agent to interact with the system.

4
00:00:20.600 --> 00:00:27.440
These tools can be high-level utilities to transform inputs, or they can be task-specific.

5
00:00:27.440 --> 00:00:31.640
Agents can even use chains and other agents as tools!

6
00:00:31.640 --> 00:00:36.240
In this video, we'll discuss a type of agent called ReAct agents.

7
00:00:36.240 --> 00:00:41.640
ReAct stands for reasoning and acting, and this is exactly how the agent operates.

8
00:00:41.640 --> 00:00:46.360
It prompts the model using a repeated loop of thinking, acting, and observing.

9
00:00:46.360 --> 00:00:51.720
If we were to ask a ReAct agent that had access to a weather tool, "What is the weather

10
00:00:51.720 --> 00:00:57.120
like in Kingston, Jamaica?", it would start by thinking about the task and which tool

11
00:00:57.120 --> 00:01:02.960
to call, call that tool using the information, and observe the results from the tool call?"

12
00:01:02.960 --> 00:01:09.200
To implement agents, we'll be using LangGraph, which is branch of the LangChain

13
00:01:09.200 --> 00:01:16.240
ecosystem specifically for designing agentic systems, or systems including agents.

14
00:01:16.240 --> 00:01:22.720
Like LangChain's core library, it's is built to provide a unified, tool-agnostic syntax.

15
00:01:22.720 --> 00:01:26.200
We'll be using the following version in this course.

16
00:01:26.200 --> 00:01:32.280
We'll create a ReAct agent that can solve math problems - something most LLMs struggle with.

17
00:01:32.280 --> 00:01:37.680
We import create_react_agent from langgraph and the load_tools() function.

18
00:01:37.680 --> 00:01:44.000
We initialize our LLM, and load the llm-math tool using the load_tools() function.

19
00:01:44.000 --> 00:01:47.560
To create the agent, we pass the LLM and tools to

20
00:01:47.560 --> 00:01:48.840
create_react_agent(),

21
00:01:48.840 --> 00:01:50.320


22
00:01:50.320 --> 00:01:55.080
Just like chains, agents can be executed with the .invoke() method.

23
00:01:55.080 --> 00:02:02.600
Here, we pass the chat model a message to find the square root of 101, which isn't a whole number .

24
00:02:02.600 --> 00:02:06.840
Let's see how the agent approaches the problem.

25
00:02:06.840 --> 00:02:11.320
There's a lot of metadata in the output, so we've trimmed it for brevity.

26
00:02:11.320 --> 00:02:15.640
We can see that executing the agent resulted in a series of messages!

27
00:02:15.640 --> 00:02:22.320
The first is our prompt defining the problem; the second is created by the model to identify the tool to use

28
00:02:22.320 --> 00:02:28.520
and to convert our query into mathematical format; the third is the result of the tool call, and the final

29
00:02:28.520 --> 00:02:35.640
message is the model's response after observing the tool's answer, which it decided to round to two decimal places.

30
00:02:35.640 --> 00:02:39.000
If we just want the final response, we can subset the final

31
00:02:39.000 --> 00:02:43.640
message and extract it's content with the .content attribute.

32
00:02:43.640 --> 00:02:47.560
Now let's begin implementing agents.

33
00:02:47.560 --> 00:02:47.600
.

