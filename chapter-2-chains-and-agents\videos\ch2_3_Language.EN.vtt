WEBVTT

1
00:00:00.000 --> 00:00:07.840
Now that we've created our first agent, let's take a closer a look at tools so we can design our own.

2
00:00:07.840 --> 00:00:14.680
Tools in LangChain must be formatted in a specific way to be compatible with agents.

3
00:00:14.680 --> 00:00:19.200
They must have a name, accessible via the .name attribute .

4
00:00:19.200 --> 00:00:24.040
A description, which is used by the LLM to determine when to call the tool.

5
00:00:24.040 --> 00:00:27.880
In this case, if the LLM interprets the task as being a math

6
00:00:27.880 --> 00:00:32.480
problem, it will likely call this tool based on its description.

7
00:00:32.480 --> 00:00:36.760
Finally, the return_direct parameter indicates whether the agent

8
00:00:36.760 --> 00:00:41.560
should stop after invoking this tool, which it won't in this case.

9
00:00:41.560 --> 00:00:47.400
Understanding this required format will help us to understand how to create our own tools.

10
00:00:47.400 --> 00:00:54.120
Let's say we want to define a Python function to generate a financial report for a company.

11
00:00:54.120 --> 00:01:03.120
It takes three arguments: the company_name, revenue, and expenses, and outputs a string containing the net_income.

12
00:01:03.120 --> 00:01:09.120
We make the use of this function clear in the docstring, defined using triple quotes.

13
00:01:09.120 --> 00:01:11.480
Here's what the report looks like.

14
00:01:11.480 --> 00:01:16.040
Let's convert this function into a tool our agent can call.

15
00:01:16.040 --> 00:01:21.760
To do this, we import the @tool decorator and add it before the function definition.

16
00:01:21.760 --> 00:01:26.200
Don't worry if you're not familiar with Python decorators; the @tool

17
00:01:26.200 --> 00:01:31.640
modifies the function so it's in the correct format to be used by a tool.

18
00:01:31.640 --> 00:01:38.000
Like with the built-in tool we were looking at, we can now examine the various attributes of our tool.

19
00:01:38.000 --> 00:01:42.760
These include its name, which is the function name by default, its description,

20
00:01:42.760 --> 00:01:49.000
which is the function's docstring, and return_direct, which is set to False by default.

21
00:01:49.000 --> 00:01:55.080
We can also print the tools arguments, which lay out the argument names and expected data types.

22
00:01:55.080 --> 00:01:58.080
Let's put our tool into action.

23
00:01:58.080 --> 00:02:06.280
We'll again use a ReAct agent, combining the chat LLM with a list of tools to use, containing our new custom tool!

24
00:02:06.280 --> 00:02:14.200
We invoke the agent with an input containing the required information: a company name, revenue, and expenses.

25
00:02:14.200 --> 00:02:18.280
The response from the agent starts with our input, then determines that the

26
00:02:18.280 --> 00:02:23.160
financial_report tool should be called, which returns a tool message containing the

27
00:02:23.160 --> 00:02:30.360
output from our function, and finally, the output is passed to the LLM, which responds to us.

28
00:02:30.360 --> 00:02:33.440
Let's zoom in on this final message.

29
00:02:33.440 --> 00:02:35.880
Here's the final output from the LLM.

30
00:02:35.880 --> 00:02:38.240
Notice anything.

31
00:02:38.240 --> 00:02:42.040
Here's the output from the tool based on the function we defined?

32
00:02:42.040 --> 00:02:47.400
Notice that there's slight formatting differences between the two; the LLM received

33
00:02:47.400 --> 00:02:52.320
the tool output, and put it's own slight spin on it, which you may need to watch out for.

34
00:02:52.320 --> 00:02:52.760
Now it's your turn.

35
00:02:52.760 --> 00:02:56.800
!

