
#  PDF document loaders

```
Exercise ID 1760697
```

##  Assignment 

To begin implementing Retrieval Augmented Generation (RAG), you'll first need to load the documents that the model will access. These documents can come from a variety of sources, and LangChain supports document loaders for many of them.

In this exercise, you'll use a document loader to load a PDF document containing the paper, **RAG VS Fine-Tuning: Pipelines, Tradeoffs, and a Case Study on Agriculture** by <PERSON><PERSON><PERSON> et al. (2024).

**Note**: `pypdf`, a dependency for loading PDF documents in LangChain, has already been installed for you.

##  Pre exercise code 

```
import shutil

# Copy file so learners don't need to add directory path
shutil.copy("/usr/local/share/datasets/2401.08406v3.pdf", "rag_vs_fine_tuning.pdf")
```



##  Instructions 

- Import the appropriate class for loading PDF documents in LangChain.
- Create a document loader for the `'rag_vs_fine_tuning.pdf'` document, which is available in the current directory.
- Load the document into memory to view the contents of the first document, or page.



```
# Import library
from langchain_community.document_loaders import ____

# Create a document loader for rag_vs_fine_tuning.pdf
loader = ____

# Load the document
data = ____
print(data[0])
```

##  Hints 

- The `PyPDFLoader` class can load PDFs from a given file path.
- Load the document into memory with the `.load()` method.



##  Solution 

```
# Import library
from langchain_community.document_loaders import PyPDFLoader

# Create a document loader for rag_vs_fine_tuning.pdf
loader = PyPDFLoader('rag_vs_fine_tuning.pdf')

# Load the document
data = loader.load()
print(data[0])
```


