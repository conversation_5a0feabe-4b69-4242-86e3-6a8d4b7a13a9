
#  CSV document loaders

```
Exercise ID 1760698
```

##  Assignment 

Comma-separated value (CSV) files are an extremely common file format, particularly in data-related fields. Fortunately, LangChain provides different document loaders for different formats, keeping almost all of the syntax the same!

In this exercise, you'll use a document loader to load a CSV file containing data on FIFA World Cup international viewership. If your interested in the full analysis behind this data, check out [How to Break FIFA](https://fivethirtyeight.com/features/how-to-break-fifa/).

##  Pre exercise code 

```
import shutil

# Copy file so learners don't need to add directory path
shutil.copy("/usr/local/share/datasets/fifa_countries_audience.csv", "fifa_countries_audience.csv")
```



##  Instructions 

- Import the appropriate class for loading CSV documents in LangChain.
- Create a document loader for the `'fifa_countries_audience.csv'` document, which is available in the current directory.
- Load the documents into memory to view the contents of the first document.



```
# Import library
from langchain_community.document_loaders.csv_loader import ____

# Create a document loader for fifa_countries_audience.csv
loader = ____

# Load the document
data = ____
print(data[0])
```

##  Hints 

- The `CSVLoader` class can load CSVs from a given file path.



##  Solution 

```
# Import library
from langchain_community.document_loaders.csv_loader import CSVLoader

# Create a document loader for fifa_countries_audience.csv
loader = CSVLoader('fifa_countries_audience.csv')

# Load the document
data = loader.load()
print(data[0])
```


