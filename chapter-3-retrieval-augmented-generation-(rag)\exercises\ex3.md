
#  HTML document loaders

```
Exercise ID 1760699
```

##  Assignment 

It's possible to load documents from many different formats, including complex formats like HTML.

In this exercise, you'll load an HTML file containing a White House executive order.

##  Pre exercise code 

```
import shutil

# Copy file so learners don't need to add directory path
shutil.copy("/usr/local/share/datasets/white_house_executive_order_nov_2023.html", "white_house_executive_order_nov_2023.html")
```



##  Instructions 

- Use the `UnstructuredHTMLLoader` class to load the `white_house_executive_order_nov_2023.html` file in the current directory.
- Load the documents into memory.
- Print the first document.
- Print the first document's metadata.



```
from langchain_community.document_loaders import UnstructuredHTMLLoader

# Create a document loader for unstructured HTML
loader = ____

# Load the document
data = ____

# Print the first document
print(____)

# Print the first document's metadata
print(____)
```

##  Hints 

- To access a document loader's metadata, access its `.metadata` attribute.



##  Solution 

```
from langchain_community.document_loaders import UnstructuredHTMLLoader 

# Create a document loader for unstructured HTML
loader = UnstructuredHTMLLoader("white_house_executive_order_nov_2023.html")

# Load the document
data = loader.load()

# Print the first document
print(data[0])

# Print the first document's metadata
print(data[0].metadata)
```


