
#  Splitting HTML

```
Exercise ID 1760703
```

##  Assignment 

In this exercise, you'll split an HTML containing an executive order on AI created by the US White House in October 2023. To retain as much context as possible in the chunks, you'll split using larger `chunk_size` and `chunk_overlap` values.

All of the Lang<PERSON>hain classes necessary for completing this exercise have been pre-loaded for you.

##  Pre exercise code 

```
import shutil
from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain_community.document_loaders import UnstructuredHTMLLoader

# Copy file so learners don't need to add directory path
shutil.copy("/usr/local/share/datasets/white_house_executive_order_nov_2023.html", "white_house_executive_order_nov_2023.html")
```



##  Instructions 

- Create a document loader for `white_house_executive_order_nov_2023.html`, and load it into memory.
- Set a `chunk_size` of `300` and a `chunk_overlap` of `100`.
- Define the splitter, splitting on the `'.'` character, and use it to split `data` and print the chunks.



```
# Load the HTML document into memory
loader = ____
data = ____

# Define variables
chunk_size = ____
chunk_overlap = ____

# Split the HTML
splitter = RecursiveCharacterTextSplitter(
    chunk_size=chunk_size,
    chunk_overlap=chunk_overlap,
    separators=____)

docs = ____
print(docs)
```

##  Hints 

- To load documents into memory from a document loader, call the `.load()` method.
- The `separators` argument of `RecursiveCharacterTextSplitter` takes a list of strings for each of the characters to recursively split on.



##  Solution 

```
# Load the HTML document into memory
loader = UnstructuredHTMLLoader("white_house_executive_order_nov_2023.html")
data = loader.load()

# Define variables
chunk_size = 300
chunk_overlap = 100

# Split the HTML
splitter = RecursiveCharacterTextSplitter(
    chunk_size=chunk_size,
    chunk_overlap=chunk_overlap,
    separators=['.'])

docs = splitter.split_documents(data) 
print(docs)
```


