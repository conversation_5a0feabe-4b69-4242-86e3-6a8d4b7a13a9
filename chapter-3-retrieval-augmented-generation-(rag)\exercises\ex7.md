
#  Preparing the documents and vector database

```
Exercise ID 1760705
```

##  Assignment 

Over the next few exercises, you'll build a full RAG workflow to have a conversation with a PDF document containing the paper, **RAG VS Fine-Tuning: Pipelines, Tradeoffs, and a Case Study on Agriculture** by <PERSON><PERSON><PERSON> et al. (2024). This works by splitting the documents into chunks, storing them in a vector database, defining a prompt to connect the retrieved documents and user input, and building a retrieval chain for the LLM to access this external data.

In this exercise, you'll prepare the document for storage and ingest them into a Chroma vector database. You'll use a `RecursiveCharacterTextSplitter` to chunk the PDF, and ingest them into a Chroma vector database using an OpenAI embeddings function.

The following classes have already been imported for you: `RecursiveCharacterTextSplitter`, `Chroma`, and `OpenAIEmbeddings`.

##  Pre exercise code 

```
# These three lines swap the stdlib sqlite3 lib with the pysqlite3 package for this Chroma version 
__import__('pysqlite3')
import sys
import os
sys.modules['sqlite3'] = sys.modules.pop('pysqlite3')

from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain_chroma import Chroma
from langchain_openai import OpenAIEmbeddings
from langchain_community.document_loaders import PyPDFLoader

import shutil
# Copy file so learners don't need to add directory path
shutil.copy("/usr/local/share/datasets/2401.08406v3.pdf", "rag_vs_fine_tuning.pdf")
```



##  Instructions 

- Assign your OpenAI API key to `openai_api_key`.
- Split the documents in `data` using a recursive character splitter with a `chunk_size` of `300` and `chunk_overlap` of `50`; leave the `separators` argument out, as it defaults to `["\n\n", "\n", " ", ""]`.
- Define an OpenAI embeddings model and use it to embed and ingest the documents into a Chroma database.
- Configure `vectorstore` into a retriever object that returns the top **3** documents for use in the final RAG chain.



```
# Set your API Key from OpenAI
openai_api_key = '____'

loader = PyPDFLoader('rag_vs_fine_tuning.pdf')
data = loader.load()

# Split the document using RecursiveCharacterTextSplitter
splitter = ____
docs = splitter.____(data) 

# Embed the documents in a persistent Chroma vector database
embedding_function = ____(openai_api_key=openai_api_key)
vectorstore = Chroma.____(
    ____,
    embedding=____,
    persist_directory=os.getcwd()
)

# Configure the vector store as a retriever
retriever = ____(
    search_type="similarity",
    search_kwargs={____}
)
```

##  Hints 

- Use the `RecursiveCharacterTextSplitter()` function for the document splitting.
- The `.from_documents()` `Chroma` method is used to create a Chroma vector database from a set of documents.
- The `search_kwargs` argument takes a dictionary where the `"k"` parameter can be changed to specify the number of documents to retrieve.



##  Solution 

```
# Set your API Key from OpenAI
openai_api_key = '<OPENAI_API_TOKEN>'

loader = PyPDFLoader('rag_vs_fine_tuning.pdf')
data = loader.load()

# Split the document using RecursiveCharacterTextSplitter
splitter = RecursiveCharacterTextSplitter(
    chunk_size=300,
    chunk_overlap=50)
docs = splitter.split_documents(data) 

# Embed the documents in a persistent Chroma vector database
embedding_function = OpenAIEmbeddings(openai_api_key=openai_api_key)
vectorstore = Chroma.from_documents(
    docs,
    embedding=embedding_function,
    persist_directory=os.getcwd()
)

# Configure the vector store as a retriever
retriever = vectorstore.as_retriever(
    search_type="similarity",
    search_kwargs={"k": 3}
)
```


