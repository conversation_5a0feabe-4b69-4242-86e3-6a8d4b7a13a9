
#  Building a retrieval prompt template

```
Exercise ID 1760706
```

##  Assignment 

Now your documents have been ingested into vector database and are ready for retrieval, you'll need to design a chat prompt template to combine the retrieved document chunks with the user input question.

The general structure of the prompt has already been provided; your goal is to insert the correct input variable placeholders into the `message` string and convert the string into a chat prompt template.

##  Pre exercise code 

```
from langchain_core.prompts import ChatPromptTemplate
```



##  Instructions 

- Complete the message string to add a placeholder for dynamic insertion of the retrieved documents called `context` and user input question `question`.
- Create a chat prompt template from `message`.



```
# Add placeholders to the message string
message = """
Answer the following question using the context provided:

Context:
____

Question:
____

Answer:
"""

# Create a chat prompt template from the message string
prompt_template = ChatPromptTemplate.____([("____", ____)])
```

##  Hints 

- Placeholders are added to prompt templates using dynamic string insertion: `'Prompt text: {placeholder_name}'`.
- The `.from_messages()` method is used to convert strings into prompt templates for chat models.



##  Solution 

```
# Add placeholders to the message string
message = """
Answer the following question using the context provided:

Context:
{context}

Question:
{question}

Answer:
"""

# Create a chat prompt template from the message string
prompt_template = ChatPromptTemplate.from_messages([("human", message)])
```


