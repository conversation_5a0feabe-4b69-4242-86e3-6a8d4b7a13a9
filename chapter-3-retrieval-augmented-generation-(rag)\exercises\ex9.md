
#  Creating a RAG chain

```
Exercise ID 1760707
```

##  Assignment 

Now to bring all the components together in your RAG workflow! You've prepared the documents and ingested them into a Chroma database for retrieval. You created a prompt template to include the retrieved chunks from the academic paper and answer questions.

The prompt template you created in the previous exercise is available as `prompt_template`, and the code to recreate your `retriever` has be included in the script.

##  Pre exercise code 

```
# These three lines swap the stdlib sqlite3 lib with the pysqlite3 package
__import__('pysqlite3')
import sys
sys.modules['sqlite3'] = sys.modules.pop('pysqlite3')
import os
from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain_chroma import Chroma
from langchain_openai import OpenAIEmbeddings, ChatOpenAI
from langchain_community.document_loaders import PyPDFLoader
from langchain_core.prompts import ChatPromptTemplate
from langchain_core.runnables import RunnablePassthrough

import shutil
# Copy file so learners don't need to add directory path
shutil.copy("/usr/local/share/datasets/2401.08406v3.pdf", "rag_vs_fine_tuning.pdf")

loader = PyPDFLoader('rag_vs_fine_tuning.pdf')
data = loader.load()

splitter = RecursiveCharacterTextSplitter(
    chunk_size=300,
    chunk_overlap=50)
docs = splitter.split_documents(data) 

message = """
Answer the following question using the context provided:

Context:
{context}

Question:
{question}

Answer:
"""

prompt_template = ChatPromptTemplate.from_messages([("human", message)])
```



##  Instructions 

- Assign your OpenAI API key to `openai_api_key`.
- Create an LCEL chain to link `retriever`, `prompt_template`, and `llm` so the model can retrieve the documents.
- Invoke the chain on the `'question'` provided.



```
# Set your API Key from OpenAI
openai_api_key = '____'

vectorstore = Chroma.from_documents(
    docs,
    embedding=OpenAIEmbeddings(openai_api_key=openai_api_key),
    persist_directory=os.getcwd()
)

retriever = vectorstore.as_retriever(
    search_type="similarity",
    search_kwargs={"k": 3}
)

llm = ChatOpenAI(model_name="gpt-3.5-turbo", temperature=0, openai_api_key=openai_api_key)

# Create a chain to link retriever, prompt_template, and llm
rag_chain = ({"context": ____, "question": ____}
            | ____
            | ____)

# Invoke the chain
response = ____("Which popular LLMs were considered in the paper?")
print(response.content)
```

##  Hints 

- When creating the chain, assign the retriever to the `"context"` prompt template input variable, and assign `"question"` to a runnable that allows for inputs to be entered.
- Pipe into the prompt template before the LLM.



##  Solution 

```
# Set your API Key from OpenAI
openai_api_key = '<OPENAI_API_TOKEN>'

vectorstore = Chroma.from_documents(
    docs,
    embedding=OpenAIEmbeddings(openai_api_key=openai_api_key),
    persist_directory=os.getcwd()
)

retriever = vectorstore.as_retriever(
    search_type="similarity",
    search_kwargs={"k": 3}
)

llm = ChatOpenAI(model_name="gpt-3.5-turbo", temperature=0, openai_api_key=openai_api_key)

# Create a chain to link retriever, prompt_template, and llm
rag_chain = ({"context": retriever, "question": RunnablePassthrough()}
            | prompt_template
            | llm)

# Invoke the chain
response = rag_chain.invoke("Which popular LLMs were considered in the paper?")
print(response.content)
```


