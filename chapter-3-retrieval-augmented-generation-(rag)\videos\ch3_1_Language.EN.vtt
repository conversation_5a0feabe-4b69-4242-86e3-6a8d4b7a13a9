WEBVTT

1
00:00:00.000 --> 00:00:06.440
In this chapter, we'll discuss retrieval augmented generation, or RAG.

2
00:00:06.440 --> 00:00:11.440
Pre-trained language models don't have access to external data

3
00:00:11.440 --> 00:00:16.000
sources - their understanding comes purely from their training data.

4
00:00:16.000 --> 00:00:21.680
This means that if we require our model to have knowledge that goes beyond its training data, which

5
00:00:21.680 --> 00:00:28.480
could be company data or knowledge of more recent world events, we need a way of integrating that data.

6
00:00:28.480 --> 00:00:35.040
In RAG, a user query is embedded and used to retrieve the most relevant documents from the database.

7
00:00:35.040 --> 00:00:42.640
Then, these documents are added to the model's prompt so that the model has extra context to inform its response.

8
00:00:42.640 --> 00:00:46.240
There are three primary steps to RAG development in LangChain.

9
00:00:46.240 --> 00:00:50.600
The first is loading the documents into LangChain with document loaders.

10
00:00:50.600 --> 00:00:53.560
Next, is splitting the documents into chunks.

11
00:00:53.560 --> 00:00:58.400
Chunks are units of information that we can index and process individually.

12
00:00:58.400 --> 00:01:02.600
The last step is encoding and storing the chunks for retrieval, which

13
00:01:02.600 --> 00:01:07.400
could utilize a vector database if that meets the needs of the use case.

14
00:01:07.400 --> 00:01:13.960
We'll discuss all of these steps throughout the next chapter, but for now, we'll start with document loaders.

15
00:01:13.960 --> 00:01:21.880
LangChain document loaders are classes designed to load and configure documents for integration with AI systems.

16
00:01:21.880 --> 00:01:29.080
LangChain provides document loader classes for common file types such as CSV and PDFs.

17
00:01:29.080 --> 00:01:34.200
There are also additional loaders provided by 3rd parties for managing unique document

18
00:01:34.200 --> 00:01:42.360
formats, including Amazon S3 files, Jupyter notebooks, audio transcripts, and many more.

19
00:01:42.360 --> 00:01:51.200
In this video, we will practice loading data from three common formats: PDFs, CSVs, and HTML.

20
00:01:51.200 --> 00:01:54.320
LangChain has excellent documentation on all of its document

21
00:01:54.320 --> 00:02:00.560
loaders, and there's a lot of overlap in syntax, so explore at your leisure!

22
00:02:00.560 --> 00:02:07.080
There are a few different types of PDF loaders in LangChain, and there is documentation available online for each.

23
00:02:07.080 --> 00:02:10.880
In this video, we'll use the PyPDFLoader.

24
00:02:10.880 --> 00:02:18.560
We instantiate the PyPDFLoader class, passing in the path to the PDF file we're loading.

25
00:02:18.560 --> 00:02:22.200
Finally, we use the .load() method to load the document into

26
00:02:22.200 --> 00:02:26.520
memory, and assign the resulting object to the data variable .

27
00:02:26.520 --> 00:02:29.840
We can then check the output to confirm that we have loaded it.

28
00:02:29.840 --> 00:02:35.600
Note that this document loader requires installation of the pypdf package as a dependency.

29
00:02:35.600 --> 00:02:43.080
When loading CSVs, the syntax is very similar, but instead we use the CSVLoader class.

30
00:02:43.080 --> 00:02:45.520
We're seeing a pattern forming.

31
00:02:45.520 --> 00:02:51.400
Finally, we can load HTML files using the UnstructuredHTMLLoader class!

32
00:02:51.400 --> 00:02:55.240
We can access the document's contents, again, with subsetting,

33
00:02:55.240 --> 00:02:58.200
and extract the document's metadata with the metadata attribute.

34
00:02:58.200 --> 00:03:00.720
Time to begin loading documents for RAG.

35
00:03:00.720 --> 00:03:03.240
!

