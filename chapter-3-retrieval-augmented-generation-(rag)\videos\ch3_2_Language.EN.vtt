WEBVTT

1
00:00:00.000 --> 00:00:07.120
Now that we've loaded documents from different sources, let's learn how to parse the information.

2
00:00:07.120 --> 00:00:14.960
Document splitting splits the loaded document into smaller parts, which are also called chunks.

3
00:00:14.960 --> 00:00:22.160
Chunking is particularly useful for breaking up long documents so that they fit within an LLM's context window.

4
00:00:22.160 --> 00:00:27.880
Let's examine the introduction from an academic paper, which is saved as a PDF.

5
00:00:27.880 --> 00:00:31.960
One naive splitting option would be to separate the document by-line.

6
00:00:31.960 --> 00:00:36.440
This would be simple to implement, but because sentences are often split over

7
00:00:36.440 --> 00:00:43.720
multiple lines, and because those lines are processed separately, key context might be lost.

8
00:00:43.720 --> 00:00:49.720
To counteract lost context during chunk splitting, a chunk overlap is often implemented.

9
00:00:49.720 --> 00:00:54.120
We've selected two chunks and a chunk overlap shown in green.

10
00:00:54.120 --> 00:01:00.040
Having this extra overlap present in both chunks helps retain context.

11
00:01:00.040 --> 00:01:04.400
If a model shows signs of losing context and misunderstanding information

12
00:01:04.400 --> 00:01:10.760
when answering from external sources, we may need to increase this chunk overlap.

13
00:01:10.760 --> 00:01:15.480
There isn't one document splitting strategy that works for all situations.

14
00:01:15.480 --> 00:01:18.520
We should experiment with multiple methods, and see which one

15
00:01:18.520 --> 00:01:24.200
strikes the right balance between retaining context and managing chunk size.

16
00:01:24.200 --> 00:01:31.960
We will compare two document splitting methods: CharacterTextSplitter and RecursiveCharacterTextSplitter.

17
00:01:31.960 --> 00:01:39.840
Optimizing this document splitting is an active area of research, so keep an eye out for new developments!

18
00:01:39.840 --> 00:01:47.360
As an example, let's split this quote by Elbert Hubbard, which contains 103 characters, into chunks.

19
00:01:47.360 --> 00:01:49.560
We'll compare how the two methods perform

20
00:01:49.560 --> 00:01:56.920
on this quote with a chunk_size of 24 characters and a small chunk_overlap of three.

21
00:01:56.920 --> 00:01:59.720
Let's start with CharacterTextSplitter.

22
00:01:59.720 --> 00:02:08.160
This method splits based on the separator first, then evaluates chunk_size and chunk_overlap to check if it's satisfied.

23
00:02:08.160 --> 00:02:15.960
We call CharacterTextSplitter, passing the separator to split on, along with the chunk_size and chunk_overlap.

24
00:02:15.960 --> 00:02:21.720
Applying the splitter to the quote with the .split_text() method, and printing the output, we can

25
00:02:21.720 --> 00:02:27.840
see that we have a problem: each of these chunks contains more characters than our specified chunk_size .

26
00:02:27.840 --> 00:02:34.000
CharacterTextSplitter splits on the separator in an attempt to make chunks smaller than

27
00:02:34.000 --> 00:02:41.400
chunk_size, but in this case, splitting on the separator was unable to return chunks below our chunk_size.

28
00:02:41.400 --> 00:02:44.760
Let's take a look at a more robust splitting method.

29
00:02:44.760 --> 00:02:50.080
RecursiveCharacterSplitter takes a list of separators to split on, and it works

30
00:02:50.080 --> 00:02:54.080
through the list from left to right, splitting the document using each separator

31
00:02:54.080 --> 00:03:00.520
in turn, and seeing if these chunks can be combined while remaining under chunk_size!

32
00:03:00.520 --> 00:03:05.680
Let's split the quote using the same chunk_size and chunk_overlap.

33
00:03:05.680 --> 00:03:08.720
Notice how the length of each chunk varies.

34
00:03:08.720 --> 00:03:16.760
The class split by paragraphs first, and found that the chunk size was too big; likewise for sentences.

35
00:03:16.760 --> 00:03:21.080
It got to the third separator: splitting words using the space separator, and found that

36
00:03:21.080 --> 00:03:26.520
words could be combined into chunks while remaining under the chunk_size character limit.

37
00:03:26.520 --> 00:03:30.160
However, some of these chunks are too small to contain meaningful

38
00:03:30.160 --> 00:03:35.960
context, but this recursive implementation may work better on larger documents.

39
00:03:35.960 --> 00:03:40.040
We can also use split other file formats, like HTML.

40
00:03:40.040 --> 00:03:44.160
Recall that we can load HTML using UnstructuredHTMLLoader.

41
00:03:44.160 --> 00:03:48.080
Defining the splitter is the same, but for splitting documents, we use

42
00:03:48.080 --> 00:03:51.800
the .split_documents() method instead of .split_text() to perform the split.

43
00:03:51.800 --> 00:03:54.200
Time to practice .

44
00:03:54.200 --> 00:03:55.720
.

