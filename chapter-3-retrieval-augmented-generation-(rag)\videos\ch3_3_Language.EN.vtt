WEBVTT

1
00:00:00.000 --> 00:00:02.760
Welcome back!

2
00:00:02.760 --> 00:00:07.240
Now that we've covered document loading and splitting, we'll round-out the RAG

3
00:00:07.240 --> 00:00:14.240
workflow with learning about storing and retrieving this information using vector databases.

4
00:00:14.240 --> 00:00:20.600
We've now loaded documents and split them into chunks using an appropriate chunk_size and chunk_overlap.

5
00:00:20.600 --> 00:00:24.480
All that's left is to store them for retrieval.

6
00:00:24.480 --> 00:00:30.400
We'll be using a vector database to store our documents and make them available for retrieval.

7
00:00:30.400 --> 00:00:37.800
This requires embedding our text documents to create vectors that capture the semantic meaning of the text.

8
00:00:37.800 --> 00:00:41.760
Then, a user query can be embedded to retrieve the most similar

9
00:00:41.760 --> 00:00:47.240
documents from the database and insert them into the model prompt.

10
00:00:47.240 --> 00:00:51.400
There are many vector databases available in LangChain.

11
00:00:51.400 --> 00:00:55.560
When making the decision on which solution to choose, consider whether an open

12
00:00:55.560 --> 00:01:03.160
source solution is required, which may be the case if high customizability is required.

13
00:01:03.160 --> 00:01:12.160
Also, consider whether the data can be stored on off-premises on third-party servers - not all cases will permit this.

14
00:01:12.160 --> 00:01:18.120
The amount of storage and latency of retrieving results is also a key consideration.

15
00:01:18.120 --> 00:01:25.320
Sometimes a lightweight in-memory database will be sufficient, but others will require something more powerful.

16
00:01:25.320 --> 00:01:31.120
In this course, we will use ChromaDB because it is lightweight and quick to set up.

17
00:01:31.120 --> 00:01:36.120
We'll be storing documents containing guidelines for a company's marketing copy.

18
00:01:36.120 --> 00:01:43.000
There's two guidelines: one around brand capitalization, and another on how to refer to users.

19
00:01:43.000 --> 00:01:47.000
Now that we've parsed the data, it's time to embed it.

20
00:01:47.000 --> 00:01:56.080
We'll use an embedding model from OpenAI by instantiating the OpenAIEmbeddings class, passing in our openai_api_key.

21
00:01:56.080 --> 00:02:01.360
To create a Chroma database from a set of documents, call the .from_documents()

22
00:02:01.360 --> 00:02:07.560
method on the Chroma class, passing the documents and embedding function to use .

23
00:02:07.560 --> 00:02:15.880
We'd like to persist this database to disk for future use, so provide a path to the persist_directory argument.

24
00:02:15.880 --> 00:02:19.800
Finally, to integrate the database with other LangChain components,

25
00:02:19.800 --> 00:02:24.920
we need to convert it into a retriever with the .as_retriever() method.

26
00:02:24.920 --> 00:02:28.560
Here, we specify that we want to perform a similarity search

27
00:02:28.560 --> 00:02:34.120
and return the top two most similar documents for each user query .

28
00:02:34.120 --> 00:02:38.280
So the model know what to do, we'll construct a prompt template, which starts with

29
00:02:38.280 --> 00:02:43.920
the instruction: to review and fix the copy provided, insert the retrieved guidelines

30
00:02:43.920 --> 00:02:49.200
and copy to review, and an indication that the model should follow with a fixed version.

31
00:02:49.200 --> 00:02:55.560
To chain together our retriever, prompt_template, and LLM, we use LCEL

32
00:02:55.560 --> 00:03:00.040
in a similar way as before, using pipes to connect the three components.

33
00:03:00.040 --> 00:03:04.080
The only difference is that we create a dictionary that assigns the retrieved

34
00:03:04.080 --> 00:03:08.160
documents to guidelines, and assigns the copy to review to the

35
00:03:08.160 --> 00:03:13.720
RunnablePassthrough function, which acts as a placeholder to insert our input when we invoke the chain.

36
00:03:13.720 --> 00:03:19.920
Printing the result, we can see the model fixed the two guideline breaches.

37
00:03:19.920 --> 00:03:23.760
Let's cement this with some exercises.

38
00:03:23.760 --> 00:03:23.800
.

