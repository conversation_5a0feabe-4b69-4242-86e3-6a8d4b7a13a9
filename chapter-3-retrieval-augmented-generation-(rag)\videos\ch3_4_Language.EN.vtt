WEBVTT

1
00:00:00.000 --> 00:00:04.280
Congratulations on completing the course!

2
00:00:04.280 --> 00:00:07.680
Let's wrap-up by reviewing what you've learned!

3
00:00:07.680 --> 00:00:16.120
We started by talking about the core components of LangChain: models, prompts, chains, agents, and document retrievers.

4
00:00:16.120 --> 00:00:22.360
We used open source models from Hugging Face, as well as proprietary models from OpenAI.

5
00:00:22.360 --> 00:00:27.480
We learned how to combine models, prompts, and agents using LCEL chains.

6
00:00:27.480 --> 00:00:35.200
Finally, we learned how to integrate external data into LLMs, overcoming the limitations of their training data.

7
00:00:35.200 --> 00:00:38.480
As you continue your LangChain journey into AI application

8
00:00:38.480 --> 00:00:44.120
development, the LangChain Hub will be a fantastic resource along the way.

9
00:00:44.120 --> 00:00:49.040
The Hub contains a catalog of prompts for a whole range of different tasks.

10
00:00:49.040 --> 00:00:54.800
You can search, use, and add to this massive catalog as you progress.

11
00:00:54.800 --> 00:00:58.520
LangChain Templates are also worth investigating.

12
00:00:58.520 --> 00:01:05.880
They are sets of code blocks that are ready to use out of the packet, and they cover many of the most common use cases.

13
00:01:05.880 --> 00:01:11.800
They may require minor modifications or additions, but they're often the best place to start.

14
00:01:11.800 --> 00:01:16.280
The core LangChain package that you've seen in this course is only a piece of the full LangChain

15
00:01:16.280 --> 00:01:25.480
ecosystem, which also includes LangSmith, LangServe, and LangGraph, as you've also seen in this course.

16
00:01:25.480 --> 00:01:31.880
LangSmith is used for troubleshooting and evaluating LLM applications, and LangServe is used for

17
00:01:31.880 --> 00:01:38.480
deploying these applications to production, while LangGraph, as you know, is used for multi-agent knowledge graphs.

18
00:01:38.480 --> 00:01:44.640
Continue to explore these tools as the ecosystem evolves!

19
00:01:44.640 --> 00:01:49.840
Your journey is only just beginning, so onward!

