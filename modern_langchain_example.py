#!/usr/bin/env python3
"""
Modern LangChain 0.3.x + HuggingFace Integration Example
Updated for compatibility with latest versions (2024/2025)
"""

import os
from langchain_huggingface.llms import HuggingFaceEndpoint

def main():
    print("=== Modern LangChain 0.3.x + HuggingFace Example ===\n")
    
    # Set your Hugging Face API token
    # Method 1: Set as environment variable (recommended)
    os.environ["HUGGINGFACEHUB_API_TOKEN"] = "*************************************"
    
    # Method 2: Or pass directly to the model (alternative)
    # huggingfacehub_api_token = '*************************************'
    
    print("Initializing HuggingFace model...")
    
    # Define the LLM using the modern approach
    try:
        llm = HuggingFaceEndpoint(
            repo_id="tiiuae/falcon-7b-instruct",
            # huggingfacehub_api_token=huggingfacehub_api_token  # Use this if not using env var
        )
        print("✅ Model initialized successfully!")
        
    except Exception as e:
        print(f"❌ Error initializing model: {e}")
        return
    
    # Test the model with a simple question
    question = 'Whatever you do, take care of your shoes'
    print(f"\n🤖 Asking: '{question}'")
    print("Generating response...")
    
    try:
        output = llm.invoke(question)
        print(f"\n📝 Response: {output}")
        
    except Exception as e:
        print(f"❌ Error during inference: {e}")
        return
    
    print("\n✅ Example completed successfully!")

if __name__ == "__main__":
    main()
