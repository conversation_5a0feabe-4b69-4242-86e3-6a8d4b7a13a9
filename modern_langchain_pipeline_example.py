#!/usr/bin/env python3
"""
Modern LangChain 0.3.x + HuggingFace Pipeline Integration Example
Using HuggingFacePipeline instead of HuggingFaceEndpoint to avoid API issues
"""

import os
from langchain_huggingface import HuggingFacePipeline

def main():
    print("=== Modern LangChain 0.3.x + HuggingFace Pipeline Example ===\n")
    
    # Set your Hugging Face API token (for downloading models)
    os.environ["HUGGINGFACEHUB_API_TOKEN"] = "*************************************"
    
    print("Initializing HuggingFace Pipeline model...")
    print("Note: This will download the model locally, which may take some time...")
    
    # Define the LLM using the pipeline approach (recommended)
    try:
        llm = HuggingFacePipeline.from_model_id(
            model_id="microsoft/DialoGPT-medium",  # Using a smaller model for faster testing
            task="text-generation",
            pipeline_kwargs={
                "max_new_tokens": 100,
                "temperature": 0.1,
                "do_sample": True,
                "pad_token_id": 50256,  # Set pad token to avoid warnings
            }
        )
        print("✅ Pipeline model initialized successfully!")
        
    except Exception as e:
        print(f"❌ Error initializing pipeline model: {e}")
        print("Trying with a different model...")
        
        # Fallback to an even simpler model
        try:
            llm = HuggingFacePipeline.from_model_id(
                model_id="gpt2",
                task="text-generation",
                pipeline_kwargs={
                    "max_new_tokens": 50,
                    "temperature": 0.7,
                    "do_sample": True,
                    "pad_token_id": 50256,
                }
            )
            print("✅ Fallback model (GPT-2) initialized successfully!")
            
        except Exception as e2:
            print(f"❌ Error with fallback model: {e2}")
            return
    
    # Test the model with a simple question
    question = 'Whatever you do, take care of your shoes'
    print(f"\n🤖 Asking: '{question}'")
    print("Generating response...")
    
    try:
        output = llm.invoke(question)
        print(f"\n📝 Response: {output}")
        
    except Exception as e:
        print(f"❌ Error during inference: {e}")
        return
    
    print("\n✅ Example completed successfully!")

if __name__ == "__main__":
    main()
