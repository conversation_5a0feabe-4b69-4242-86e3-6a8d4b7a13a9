#!/usr/bin/env python3
"""
LangChain + HuggingFace with Reliable Free Tier Models
Based on Perplexity search results for available models
"""

import os
from langchain_huggingface.llms import HuggingFaceEndpoint

# List of reliable models from Perplexity search
RELIABLE_MODELS = [
    "gpt2",                           # Most reliable baseline
    "distilgpt2",                     # Smaller, faster GPT-2
    "google/gemma-3-1b-it",          # Instruction-tuned, reliable
    "bigscience/bloom-560m",         # Multilingual, open LLM
    "facebook/opt-2.7b",             # Lightweight OPT model
]

def test_model(repo_id, question="Whatever you do, take care of your shoes"):
    """Test a specific model with error handling"""
    print(f"\n🧪 Testing model: {repo_id}")
    print("-" * 50)
    
    try:
        # Initialize the model
        llm = HuggingFaceEndpoint(repo_id=repo_id)
        print(f"✅ Model '{repo_id}' initialized successfully")
        
        # Test inference
        print(f"🤖 Question: '{question}'")
        response = llm.invoke(question)
        print(f"📝 Response: {response}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error with model '{repo_id}': {e}")
        return False

def main():
    print("🎯 Testing Reliable HuggingFace Models on Free Tier")
    print("=" * 60)
    
    # Set API token
    os.environ["HUGGINGFACEHUB_API_TOKEN"] = "*************************************"
    
    successful_models = []
    failed_models = []
    
    # Test each model
    for model in RELIABLE_MODELS:
        if test_model(model):
            successful_models.append(model)
        else:
            failed_models.append(model)
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 SUMMARY")
    print("=" * 60)
    
    if successful_models:
        print(f"✅ Working models ({len(successful_models)}):")
        for model in successful_models:
            print(f"   - {model}")
    
    if failed_models:
        print(f"\n❌ Failed models ({len(failed_models)}):")
        for model in failed_models:
            print(f"   - {model}")
    
    # Provide working code template
    if successful_models:
        print(f"\n🎉 SUCCESS! Use this working code:")
        print("=" * 60)
        
        working_model = successful_models[0]
        code_template = f'''
import os
from langchain_huggingface.llms import HuggingFaceEndpoint

# Set your API token
os.environ["HUGGINGFACEHUB_API_TOKEN"] = "your_token_here"

# Initialize with a working model
llm = HuggingFaceEndpoint(repo_id="{working_model}")

# Make inference
question = "Whatever you do, take care of your shoes"
response = llm.invoke(question)
print(response)
'''
        print(code_template)
    
    else:
        print("\n❌ No models worked. Check your API token and internet connection.")

if __name__ == "__main__":
    main()
