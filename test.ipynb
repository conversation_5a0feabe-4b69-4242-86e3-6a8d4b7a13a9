# Updated code for LangChain 0.3.x with modern HuggingFace integration
import os
from langchain_huggingface.llms import HuggingFaceEndpoint

# Set your Hugging Face API token
# Method 1: Set as environment variable (recommended)
os.environ["HUGGINGFACEHUB_API_TOKEN"] = "*************************************"

# Method 2: Or pass directly to the model (alternative)
# huggingfacehub_api_token = '*************************************'

# Define the LLM using the modern approach
llm = HuggingFaceEndpoint(
    repo_id="tiiuae/falcon-7b-instruct",
    # huggingfacehub_api_token=huggingfacehub_api_token  # Use this if not using env var
)

# Predict the words following the text in question
question = 'Whatever you do, take care of your shoes'
output = llm.invoke(question)

print(output)

