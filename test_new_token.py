#!/usr/bin/env python3
"""
Test the new API token and try different approaches
"""

import os

def test_token_validity():
    """Test if the new token is valid"""
    print("🔑 Testing new API token...")
    
    try:
        from huggingface_hub import HfApi
        
        token = "*************************************"
        api = HfApi(token=token)
        user_info = api.whoami()
        
        print("✅ New token is VALID!")
        print(f"   User: {user_info.get('name', 'Unknown')}")
        return True
        
    except Exception as e:
        print(f"❌ New token is invalid: {e}")
        return False

def test_direct_inference():
    """Test direct inference without Lang<PERSON>hain"""
    print("\n🧪 Testing direct HuggingFace inference...")
    
    try:
        from huggingface_hub import InferenceClient
        
        token = "*************************************"
        client = InferenceClient(token=token)
        
        # Test with gpt2
        response = client.text_generation(
            "Hello, how are you?",
            model="gpt2",
            max_new_tokens=20
        )
        
        print("✅ Direct inference works!")
        print(f"   Response: {response}")
        return True
        
    except Exception as e:
        print(f"❌ Direct inference failed: {e}")
        return False

def test_alternative_approach():
    """Try an alternative approach using requests directly"""
    print("\n🔧 Testing alternative approach...")
    
    try:
        import requests
        
        token = "*************************************"
        
        headers = {"Authorization": f"Bearer {token}"}
        
        # Test API endpoint directly
        url = "https://api-inference.huggingface.co/models/gpt2"
        data = {"inputs": "Hello, how are you?"}
        
        response = requests.post(url, headers=headers, json=data)
        
        if response.status_code == 200:
            result = response.json()
            print("✅ Direct API call works!")
            print(f"   Response: {result}")
            return True
        else:
            print(f"❌ API call failed: {response.status_code} - {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Alternative approach failed: {e}")
        return False

def main():
    print("🔍 Testing New API Token and Alternative Approaches")
    print("=" * 60)
    
    # Test 1: Token validity
    token_valid = test_token_validity()
    
    if not token_valid:
        print("\n❌ Token is invalid. Please get a new one from:")
        print("   https://huggingface.co/settings/tokens")
        return
    
    # Test 2: Direct inference
    direct_works = test_direct_inference()
    
    # Test 3: Alternative approach
    alt_works = test_alternative_approach()
    
    print("\n" + "=" * 60)
    print("📊 SUMMARY")
    print("=" * 60)
    
    if direct_works or alt_works:
        print("✅ Your token works! The issue is with LangChain compatibility.")
        print("💡 Let me provide a working solution...")
        
        # Provide working solution
        print("\n🎯 WORKING SOLUTION:")
        print("=" * 40)
        
        if direct_works:
            print("Use this direct approach:")
            print("""
from huggingface_hub import InferenceClient

token = "*************************************"
client = InferenceClient(token=token)

response = client.text_generation(
    "Whatever you do, take care of your shoes",
    model="gpt2",
    max_new_tokens=50
)
print(response)
""")
        
        if alt_works:
            print("\nOr use this requests approach:")
            print("""
import requests

token = "*************************************"
headers = {"Authorization": f"Bearer {token}"}
url = "https://api-inference.huggingface.co/models/gpt2"
data = {"inputs": "Whatever you do, take care of your shoes"}

response = requests.post(url, headers=headers, json=data)
result = response.json()
print(result[0]['generated_text'])
""")
    
    else:
        print("❌ Both approaches failed. There might be an API issue.")

if __name__ == "__main__":
    main()
