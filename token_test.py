#!/usr/bin/env python3
"""
Test HuggingFace API Token Validity
"""

import os
from huggingface_hub import Hf<PERSON><PERSON>

def test_token():
    """Test if the HuggingFace token is valid"""
    
    # The token from your code
    token = "*************************************"
    
    print("🔑 Testing HuggingFace API Token...")
    print("=" * 50)
    
    try:
        # Test token with HuggingFace API
        api = HfApi(token=token)
        user_info = api.whoami()
        
        print("✅ Token is VALID!")
        print(f"   User: {user_info.get('name', 'Unknown')}")
        print(f"   Type: {user_info.get('type', 'Unknown')}")
        
        return True
        
    except Exception as e:
        print("❌ Token is INVALID!")
        print(f"   Error: {e}")
        
        print("\n💡 How to fix:")
        print("1. Go to https://huggingface.co/settings/tokens")
        print("2. Create a new token with 'Read' permissions")
        print("3. Copy the token (starts with 'hf_')")
        print("4. Replace the token in your code")
        
        return False

def test_simple_model_access():
    """Test accessing a simple model without Lang<PERSON>hain"""
    
    print("\n🧪 Testing Direct Model Access...")
    print("=" * 50)
    
    try:
        from huggingface_hub import InferenceClient
        
        # Set token
        token = "*************************************"
        os.environ["HUGGINGFACEHUB_API_TOKEN"] = token
        
        # Test with the most reliable model
        client = InferenceClient(token=token)
        
        # Try a simple text generation
        response = client.text_generation(
            "Hello, how are you?",
            model="gpt2",
            max_new_tokens=20
        )
        
        print("✅ Direct model access works!")
        print(f"   Response: {response}")
        
        return True
        
    except Exception as e:
        print("❌ Direct model access failed!")
        print(f"   Error: {e}")
        
        return False

def main():
    print("🔍 HuggingFace Token and Model Access Test")
    print("=" * 60)
    
    # Test 1: Token validity
    token_valid = test_token()
    
    # Test 2: Direct model access (if token is valid)
    if token_valid:
        model_access = test_simple_model_access()
        
        if model_access:
            print("\n🎉 SUCCESS! Your setup should work with LangChain")
            print("=" * 60)
            print("✅ Token is valid")
            print("✅ Model access works")
            print("✅ Ready to use with LangChain!")
        else:
            print("\n⚠️  Token is valid but model access failed")
            print("This might be a temporary API issue")
    else:
        print("\n❌ Please fix your token first")

if __name__ == "__main__":
    main()
