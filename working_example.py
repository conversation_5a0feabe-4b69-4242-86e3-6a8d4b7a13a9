#!/usr/bin/env python3
"""
Working LangChain + HuggingFace Example
Compatible with the current setup
"""

import os

def test_imports():
    """Test if all imports work correctly"""
    try:
        print("Testing imports...")
        
        # Test LangChain import
        import langchain
        print(f"✅ LangChain version: {langchain.__version__}")
        
        # Test HuggingFace integration import
        from langchain_huggingface.llms import HuggingFaceEndpoint
        print("✅ HuggingFaceEndpoint import successful")
        
        # Test huggingface-hub import
        import huggingface_hub
        print(f"✅ HuggingFace Hub version: {huggingface_hub.__version__}")
        
        return True
        
    except Exception as e:
        print(f"❌ Import error: {e}")
        return False

def test_model_initialization():
    """Test model initialization without making API calls"""
    try:
        from langchain_huggingface.llms import HuggingFaceEndpoint
        
        # Set API token
        os.environ["HUGGINGFACEHUB_API_TOKEN"] = "*************************************"
        
        print("Initializing model...")
        
        # Initialize the model
        llm = HuggingFaceEndpoint(
            repo_id="tiiuae/falcon-7b-instruct"
        )
        
        print("✅ Model initialized successfully!")
        return llm
        
    except Exception as e:
        print(f"❌ Model initialization error: {e}")
        return None

def test_simple_inference(llm):
    """Test a simple inference"""
    try:
        print("Testing inference...")
        
        question = 'Whatever you do, take care of your shoes'
        print(f"Question: {question}")
        
        # Make the inference call
        output = llm.invoke(question)
        print(f"✅ Response: {output}")
        
        return True
        
    except Exception as e:
        print(f"❌ Inference error: {e}")
        return False

def main():
    print("=== LangChain + HuggingFace Compatibility Test ===\n")
    
    # Test 1: Imports
    if not test_imports():
        print("❌ Import test failed. Please check your installation.")
        return
    
    print("\n" + "="*50 + "\n")
    
    # Test 2: Model initialization
    llm = test_model_initialization()
    if llm is None:
        print("❌ Model initialization failed.")
        return
    
    print("\n" + "="*50 + "\n")
    
    # Test 3: Simple inference
    if test_simple_inference(llm):
        print("\n🎉 All tests passed! Your setup is working correctly.")
    else:
        print("\n❌ Inference test failed.")

if __name__ == "__main__":
    main()
