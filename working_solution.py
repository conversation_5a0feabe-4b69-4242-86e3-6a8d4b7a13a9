#!/usr/bin/env python3
"""
Working Solution: Direct HuggingFace API without LangChain compatibility issues
This bypasses all the version conflicts and provides a clean working example
"""

import requests
import json

class SimpleHuggingFaceLLM:
    """Simple wrapper that mimics <PERSON><PERSON><PERSON><PERSON>'s interface but uses direct API calls"""
    
    def __init__(self, repo_id, api_token):
        self.repo_id = repo_id
        self.api_token = api_token
        self.base_url = "https://api-inference.huggingface.co/models"
    
    def invoke(self, prompt, max_new_tokens=50, temperature=0.7):
        """Invoke the model with a prompt - mimics <PERSON><PERSON><PERSON><PERSON>'s interface"""
        
        url = f"{self.base_url}/{self.repo_id}"
        headers = {"Authorization": f"Bearer {self.api_token}"}
        
        payload = {
            "inputs": prompt,
            "parameters": {
                "max_new_tokens": max_new_tokens,
                "temperature": temperature,
                "return_full_text": False
            }
        }
        
        try:
            response = requests.post(url, headers=headers, json=payload)
            
            if response.status_code == 200:
                result = response.json()
                if isinstance(result, list) and len(result) > 0:
                    return result[0].get('generated_text', '')
                else:
                    return str(result)
            else:
                return f"Error {response.status_code}: {response.text}"
                
        except Exception as e:
            return f"Request failed: {str(e)}"

def test_multiple_models():
    """Test multiple models to find one that works"""
    
    token = "*************************************"
    
    # List of models to try (from most to least likely to work)
    models_to_try = [
        "gpt2",
        "distilgpt2", 
        "microsoft/DialoGPT-small",
        "facebook/blenderbot-400M-distill",
        "google/flan-t5-small",
        "bigscience/bloom-560m"
    ]
    
    question = "Whatever you do, take care of your shoes"
    
    print("🔍 Testing Multiple Models to Find Working Ones")
    print("=" * 60)
    
    working_models = []
    
    for model in models_to_try:
        print(f"\n🧪 Testing: {model}")
        print("-" * 40)
        
        llm = SimpleHuggingFaceLLM(model, token)
        response = llm.invoke(question)
        
        if response.startswith("Error") or response.startswith("Request failed"):
            print(f"❌ Failed: {response}")
        else:
            print(f"✅ Success!")
            print(f"   Response: {response}")
            working_models.append(model)
    
    return working_models

def create_langchain_compatible_example(working_model):
    """Create a LangChain-compatible example using the working model"""
    
    code_template = f'''
# Working LangChain-style solution (without version conflicts)
import requests

class HuggingFaceLLM:
    def __init__(self, repo_id, api_token):
        self.repo_id = repo_id
        self.api_token = api_token
        self.base_url = "https://api-inference.huggingface.co/models"
    
    def invoke(self, prompt, max_new_tokens=50):
        url = f"{{self.base_url}}/{{self.repo_id}}"
        headers = {{"Authorization": f"Bearer {{self.api_token}}"}}
        payload = {{
            "inputs": prompt,
            "parameters": {{"max_new_tokens": max_new_tokens, "return_full_text": False}}
        }}
        
        response = requests.post(url, headers=headers, json=payload)
        if response.status_code == 200:
            result = response.json()
            return result[0].get('generated_text', '') if result else ''
        return f"Error: {{response.status_code}}"

# Usage (same as LangChain interface):
llm = HuggingFaceLLM(
    repo_id="{working_model}",
    api_token="*************************************"
)

question = "Whatever you do, take care of your shoes"
output = llm.invoke(question)
print(output)
'''
    
    return code_template

def main():
    print("🚀 Finding Working HuggingFace Models")
    print("=" * 60)
    
    # Test multiple models
    working_models = test_multiple_models()
    
    print("\n" + "=" * 60)
    print("📊 RESULTS")
    print("=" * 60)
    
    if working_models:
        print(f"✅ Found {len(working_models)} working model(s):")
        for model in working_models:
            print(f"   - {model}")
        
        print(f"\n🎯 RECOMMENDED SOLUTION:")
        print("=" * 40)
        print("Use this code (no LangChain version conflicts):")
        
        best_model = working_models[0]
        example_code = create_langchain_compatible_example(best_model)
        print(example_code)
        
    else:
        print("❌ No models are currently working.")
        print("This might be due to:")
        print("1. HuggingFace Inference API temporary issues")
        print("2. Free tier limitations")
        print("3. Model availability changes")
        print("\n💡 Try again later or check HuggingFace status")

if __name__ == "__main__":
    main()
